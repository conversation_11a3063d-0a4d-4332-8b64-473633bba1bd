// public/service-worker.js

// This ensures the service worker activates immediately
self.addEventListener('install', (event) => {
	// console.log('Service worker installing...');
	// Skip the waiting phase
	self.skipWaiting();
});

// When the service worker activates, claim all clients
self.addEventListener('activate', (event) => {
	// console.log('Service worker activating...');
	event.waitUntil(clients.claim());
});

self.addEventListener('push', (event) => {
	try {
		const payload = event.data
			? event.data.json()
			: { title: 'New Notification', body: 'You have a new Document!' };

		event.waitUntil(
			self.registration.showNotification(
				payload.title || 'UploadDoc Notification',
				{
					body: payload.body || 'You have a new notification!',
					icon: '/icon.png',
					badge: '/icon.png',
					tag: 'uploaddoc-notification',
					requireInteraction: true,
					data: {
						url: payload.url || 'https://uploaddoc.vercel.app/dashboard',
						timestamp: Date.now(),
					},
				},
			),
		);
	} catch (error) {
		console.error('Error handling push notification:', error);
		// Fallback notification
		event.waitUntil(
			self.registration.showNotification('UploadDoc', {
				body: 'You have a new notification!',
				icon: '/icon.png',
				data: {
					url: 'https://uploaddoc.vercel.app/dashboard',
				},
			}),
		);
	}
});

// Add a new event listener for notification clicks
self.addEventListener('notificationclick', (event) => {
	// Close the notification
	event.notification.close();

	// Get the URL from the notification data
	const url =
		event.notification.data?.url || 'https://uploaddoc.vercel.app/dashboard';

	// Handle the click action
	event.waitUntil(
		clients
			.matchAll({ type: 'window', includeUncontrolled: true })
			.then((clientList) => {
				// Check if there's already a window/tab open with the target URL
				for (const client of clientList) {
					if (client.url === url && 'focus' in client) {
						return client.focus();
					}
				}
				// If no existing window/tab, open a new one
				if (clients.openWindow) {
					return clients.openWindow(url);
				}
			}),
	);
});
