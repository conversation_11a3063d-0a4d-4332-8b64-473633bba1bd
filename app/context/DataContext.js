'use client';
import {
	createContext,
	useState,
	useContext,
	useEffect,
	useCallback,
	useRef,
} from 'react';
import { AuthContext } from './AuthContext';

export const DataContext = createContext();

export const DataProvider = ({ children }) => {
	const { user, fetchWithToken, getUserId } = useContext(AuthContext);
	const [projects, setProjects] = useState([]);
	const [users, setUsers] = useState([]);
	const [adminUsers, setAdminUsers] = useState([]);
	const [loading, setLoading] = useState({
		projects: false,
		users: false,
		admins: false,
	});
	const [error, setError] = useState({
		projects: null,
		users: null,
		admins: null,
	});
	const [lastFetched, setLastFetched] = useState({
		projects: null,
		users: null,
		admins: null,
	});
	const [pagination, setPagination] = useState({
		projects: { currentPage: 1, limit: 10, totalPages: 1, totalCount: 0 },
		users: { currentPage: 1, limit: 10, totalPages: 1, totalCount: 0 },
		admins: { currentPage: 1, limit: 10, totalPages: 1, totalCount: 0 },
	});

	// Track ongoing fetch requests to prevent duplicates
	const pendingFetches = useRef({
		projects: null,
		users: null,
		admins: null,
	});

	const EXCLUDED_EMAIL = '<EMAIL>';
	const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

	// Create a stable reference to state values needed in fetchData
	const stateRef = useRef({
		adminUsers,
		users,
		projects,
		lastFetched,
		pagination,
	});

	// Update ref whenever these states change
	useEffect(() => {
		stateRef.current = {
			adminUsers,
			users,
			projects,
			lastFetched,
			pagination,
		};
	}, [adminUsers, users, projects, lastFetched, pagination]);

	const shouldRefetch = useCallback((dataType) => {
		const lastFetchTime = stateRef.current.lastFetched[dataType];
		if (!lastFetchTime) return true;
		const now = new Date().getTime();
		return now - lastFetchTime > CACHE_DURATION;
	}, []);

	// Helper function to generate a stable cache key
	const getCacheKey = useCallback(
		(dataType, paginationOptions, searchParams) => {
			const paginationKey = paginationOptions
				? `page=${paginationOptions.currentPage}&limit=${paginationOptions.limit}`
				: '';
			const searchKey = Object.keys(searchParams).length
				? `&${Object.entries(searchParams)
						.sort(([keyA], [keyB]) => keyA.localeCompare(keyB))
						.map(([key, value]) => `${key}=${value}`)
						.join('&')}`
				: '';
			return `${dataType}:${paginationKey}${searchKey}`;
		},
		[],
	);

	// Track cache by unique keys instead of just dataType
	const [cacheKeys, setCacheKeys] = useState({});

	const fetchData = useCallback(
		async (
			dataType,
			forceRefresh = false,
			paginationOptions = null,
			searchParams = {},
		) => {
			const cacheKey = getCacheKey(
				dataType,
				paginationOptions || stateRef.current.pagination[dataType],
				searchParams,
			);

			// If there's already a pending fetch for this cache key, return that promise
			if (pendingFetches.current[cacheKey]) {
				return pendingFetches.current[cacheKey];
			}

			// Get current pagination settings for this data type
			const currentPagination = stateRef.current.pagination[dataType];

			// If data is already in cache and we don't need to refresh, return it immediately
			if (
				!forceRefresh &&
				cacheKeys[cacheKey] &&
				cacheKeys[cacheKey].timestamp > Date.now() - CACHE_DURATION
			) {
				return {
					success: true,
					data: cacheKeys[cacheKey].data,
					pagination: currentPagination,
				};
			}

			// Set pagination options to use (from parameter or current state)
			const paginationToUse = paginationOptions || currentPagination;
			const { currentPage, limit } = paginationToUse;

			const fetchFunctions = {
				users: async () => {
					const response = await fetchWithToken(
						`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/users/all?page=${currentPage}&limit=${limit}`,
					);
					const data = await response.json();

					// Handle new response format with users and pagination
					if (data.users && data.pagination) {
						// Update pagination state
						setPagination((prev) => ({
							...prev,
							users: {
								currentPage: data.pagination.currentPage,
								limit: data.pagination.limit,
								totalPages: data.pagination.totalPages,
								totalCount: data.pagination.totalCount,
							},
						}));
						return data.users.filter((u) => u.email !== EXCLUDED_EMAIL);
					}
					// Handle old pagination response format
					else if (data.docs) {
						// Update pagination state
						setPagination((prev) => ({
							...prev,
							users: {
								currentPage: data.page,
								limit: data.limit,
								totalPages: data.totalPages,
								totalCount: data.totalDocs,
							},
						}));
						return data.docs.filter((u) => u.email !== EXCLUDED_EMAIL);
					}

					// Fallback for old API format
					return data.filter((u) => u.email !== EXCLUDED_EMAIL);
				},
				admins: async () => {
					// Build query parameters
					const queryParams = new URLSearchParams({
						page: currentPage,
						limit: limit,
					});

					// Add single `query` parameter if provided
					if (searchParams.query) {
						queryParams.append('query', searchParams.query);
					}

					const response = await fetchWithToken(
						`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/users/admins?${queryParams}`,
					);
					const data = await response.json();

					// Handle response format
					if (data.admins && data.pagination) {
						setPagination((prev) => ({
							...prev,
							admins: {
								currentPage: data.pagination.currentPage,
								limit: data.pagination.limit,
								totalPages: data.pagination.totalPages,
								totalCount: data.pagination.totalCount,
							},
						}));
						return data.admins.filter((u) => u.email !== EXCLUDED_EMAIL);
					}
					return data.filter((u) => u.email !== EXCLUDED_EMAIL);
				},

				projects: async () => {
					const userId = getUserId(user);
					const url =
						user?.isAdmin && !user?.superAdmin
							? `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/projects/assigned/${userId}?page=${currentPage}&limit=${limit}`
							: `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/projects/all?page=${currentPage}&limit=${limit}`;
					const response = await fetchWithToken(url);
					const data = await response.json();

					// Handle new response format with projects and pagination
					if (data.projects && data.pagination) {
						// Update pagination state
						setPagination((prev) => ({
							...prev,
							projects: {
								currentPage: data.pagination.currentPage,
								limit: data.pagination.limit,
								totalPages: data.pagination.totalPages,
								totalCount: data.pagination.totalCount,
							},
						}));
						return data.projects;
					}
					// Handle old pagination response format
					else if (data.docs) {
						// Update pagination state
						setPagination((prev) => ({
							...prev,
							projects: {
								currentPage: data.page,
								limit: data.limit,
								totalPages: data.totalPages,
								totalCount: data.totalDocs,
							},
						}));
						return data.docs;
					}

					// Fallback for old API format
					return data;
				},
			};

			setLoading((prev) => ({ ...prev, [dataType]: true }));

			// Create the fetch promise
			const fetchPromise = (async () => {
				try {
					const data = await fetchFunctions[dataType]();
					if (dataType === 'users') setUsers(data);
					else if (dataType === 'admins') setAdminUsers(data);
					else if (dataType === 'projects') setProjects(data);

					// Update cache keys with the new data, timestamp, and pagination
					const updatedPagination = stateRef.current.pagination[dataType];
					setCacheKeys((prev) => ({
						...prev,
						[cacheKey]: {
							timestamp: Date.now(),
							data,
							pagination: updatedPagination,
						},
					}));
					setError((prev) => ({ ...prev, [dataType]: null }));

					return {
						success: true,
						data,
						pagination: updatedPagination,
					};
				} catch (error) {
					setError((prev) => ({ ...prev, [dataType]: error.message }));
					return { success: false, error: error.message };
				} finally {
					setLoading((prev) => ({ ...prev, [dataType]: false }));
					// Clear the pending fetch when done
					pendingFetches.current[cacheKey] = null;
				}
			})();

			// Store the promise so we can return it for duplicate calls
			pendingFetches.current[dataType] = fetchPromise;
			return fetchPromise;
		},
		[fetchWithToken, getUserId, user, shouldRefetch],
	);

	// Initial data fetch when user is available
	useEffect(() => {
		// Allows guest users to fetch data
		fetchData('admins');
		if (user) {
			// Fetch admin users for everyone
			fetchData('admins');

			// Only fetch users and projects for logged-in admin users
			if (user?.isAdmin || user?.superAdmin) {
				Promise.all([fetchData('users'), fetchData('projects')]);
			} else {
				// For regular users, just fetch projects
				fetchData('projects');
			}
		}
	}, [user, fetchData]);

	// Update project in state after operations like accept/reject
	const updateProject = useCallback((projectId, updatedData) => {
		setProjects((prev) =>
			prev.map((p) => (p._id === projectId ? { ...p, ...updatedData } : p)),
		);
	}, []);

	// Remove project from state after deletion
	const removeProject = useCallback((projectId) => {
		setProjects((prev) => prev.filter((p) => p._id !== projectId));
	}, []);

	// Update user in state
	const updateUser = useCallback((userId, updatedData) => {
		setUsers((prev) =>
			prev.map((u) => (u._id === userId ? { ...u, ...updatedData } : u)),
		);
	}, []);

	// Function to update pagination settings and fetch data
	const updatePagination = useCallback(
		(dataType, newPagination, searchParams = {}) => {
			setPagination((prev) => ({
				...prev,
				[dataType]: {
					...prev[dataType],
					...newPagination,
				},
			}));

			// Only pass searchParams for admins
			return fetchData(
				dataType,
				true,
				{
					...stateRef.current.pagination[dataType],
					...newPagination,
				},
				dataType === 'admins' ? searchParams : {},
			);
		},
		[fetchData],
	);

	// Add a method to refresh data on demand
	const refreshData = useCallback(
		(dataType) => {
			// Clear all cache entries for this dataType
			setCacheKeys((prev) => {
				const newCache = { ...prev };
				Object.keys(newCache).forEach((key) => {
					if (key.startsWith(`${dataType}:`)) {
						delete newCache[key];
					}
				});
				return newCache;
			});
			return fetchData(dataType, true);
		},
		[fetchData],
	);

	return (
		<DataContext.Provider
			value={{
				projects,
				users,
				adminUsers,
				loading,
				error,
				pagination,
				fetchData,
				updateProject,
				removeProject,
				updateUser,
				updatePagination,
				refreshData, // Expose the refresh method
			}}>
			{children}
		</DataContext.Provider>
	);
};

// Custom hook for easier context usage
export const useData = () => useContext(DataContext);
