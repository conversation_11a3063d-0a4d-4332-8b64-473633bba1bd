'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import {
	getAnalytics,
	logEvent,
	setUserId,
	setUserProperties,
	isSupported,
} from 'firebase/analytics';
import { AuthContext } from './AuthContext';
import { app } from '../../firebase';

export const AnalyticsContext = createContext();

export const AnalyticsProvider = ({ children }) => {
	const { user } = useContext(AuthContext);
	const [analytics, setAnalytics] = useState(null);

	// Initialize analytics
	useEffect(() => {
		const initAnalytics = async () => {
			try {
				// Check if analytics is supported in the current environment
				if (await isSupported()) {
					const analyticsInstance = getAnalytics(app);
					setAnalytics(analyticsInstance);
					// console.log('Firebase Analytics initialized');
				} else {
					console.log('Firebase Analytics not supported in this environment');
				}
			} catch (error) {
				console.error('Error initializing Firebase Analytics:', error);
			}
		};

		initAnalytics();
	}, []);

	// Set user ID and properties when user changes
	useEffect(() => {
		if (!analytics || !user) return;

		try {
			// Set user ID for analytics
			const userId = user.id || user._id;
			if (userId) {
				setUserId(analytics, userId);

				// Set user properties
				setUserProperties(analytics, {
					isAdmin: user.isAdmin || false,
					isSuperAdmin: user.superAdmin || false,
					email: user.email,
					name: user.name,
				});

				// Log user login event
				logEvent(analytics, 'user_login');
			}
		} catch (error) {
			console.error('Error setting analytics user data:', error);
		}
	}, [analytics, user]);

	// Function to log events
	const trackEvent = (eventName, eventParams = {}) => {
		if (!analytics) return;

		try {
			logEvent(analytics, eventName, eventParams);
		} catch (error) {
			console.error(`Error logging event ${eventName}:`, error);
		}
	};

	// Function to track page views
	const trackPageView = (pageName, pageParams = {}) => {
		if (!analytics) return;

		try {
			logEvent(analytics, 'page_view', {
				page_title: pageName,
				page_location: window.location.href,
				page_path: window.location.pathname,
				...pageParams,
			});
		} catch (error) {
			console.error(`Error logging page view for ${pageName}:`, error);
		}
	};

	return (
		<AnalyticsContext.Provider value={{ trackEvent, trackPageView }}>
			{children}
		</AnalyticsContext.Provider>
	);
};

// Custom hook for easier context usage
export const useAnalytics = () => useContext(AnalyticsContext);
