'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { FaBug, FaHome, FaRedo } from 'react-icons/fa';
import { useAnalytics } from './context/AnalyticsContext';

export default function Error({ error, reset }) {
  const { trackEvent } = useAnalytics();
  
  useEffect(() => {
    // Log the error to analytics
    if (trackEvent) {
      trackEvent('error_page_view', {
        error_message: error?.message || 'Unknown error',
        error_stack: error?.stack?.substring(0, 500) || 'No stack trace',
        path: typeof window !== 'undefined' ? window.location.pathname : ''
      });
    }
    
    // Log to console in development
    console.error('Application error:', error);
  }, [error, trackEvent]);

  return (
    <div className="min-h-screen bg-background flex flex-col items-center justify-center p-6">
      <div className="relative">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-10 left-10 w-40 h-40 rounded-full bg-red-500 blur-3xl"></div>
          <div className="absolute bottom-10 right-10 w-60 h-60 rounded-full bg-primary blur-3xl"></div>
        </div>
        
        <div className="relative z-10 max-w-md w-full backdrop-blur-md p-8 rounded-xl shadow-lg card text-center">
          <div className="flex justify-center mb-6">
            <div className="btn-3d bg-gradient-to-r from-red-500 to-red-700 p-4 rounded-full">
              <FaBug className="w-8 h-8 text-white" />
            </div>
          </div>
          
          <h1 className="text-3xl font-bold mb-2 text-text">Something Went Wrong</h1>
          
          <p className="text-text/80 mb-8">
            We're sorry, but there was an error processing your request.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button 
              onClick={() => reset()}
              className="btn-3d bg-gradient-to-r from-primary to-accent text-white px-6 py-3 rounded-lg flex items-center justify-center gap-2 hover:opacity-90 transition-all"
            >
              <FaRedo className="w-4 h-4" />
              <span>Try Again</span>
            </button>
            
            <Link 
              href="/"
              className="glow-effect bg-primary/10 text-text px-6 py-3 rounded-lg hover:bg-primary/20 transition-all flex items-center justify-center gap-2"
            >
              <FaHome className="w-4 h-4" />
              <span>Go Home</span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
