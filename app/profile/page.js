'use client';
import Navbar from '@/components/Navbar';
import React, { useContext, useState, useEffect, useRef } from 'react'; // Import useRef

import {
	Star,
	FileText,
	Tag,
	User,
	Mail,
	Award,
	Shield,
	ShieldCheck,
	ShieldX,
} from 'lucide-react';
import Autocomplete from 'react-google-autocomplete';
import { AuthContext } from '../context/AuthContext';

const fetchWithToken = async (url, options = {}) => {
	const token = localStorage.getItem('token');
	return fetch(url, {
		...options,
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${token}`,
		},
	});
};

// Move presetOpeningHours outside the component to prevent recreation
const PRESET_OPENING_HOURS = [
	'8 AM - 5 PM',
	'9 AM - 6 PM',
	'10 AM - 7 PM',
	'Custom',
];

const ProfilePage = () => {
	const { user } = useContext(AuthContext);
	const [profile, setProfile] = useState({
		printingCost: '',
		printingLocation: '',
		openingHours: '',
		customOpeningHours: '',
		discountRates: [], // Initialize as empty array of objects
		supportContact: '',
		additionalInfo: '',
		rating: 0,
		reviews: [],
		adminStatus: 'active', // Default to active
		documentToken: 0,
		documentsReceived: 0,
		queueTimeEstimate: 0,
	});

	const [initialProfile, setInitialProfile] = useState(null); // Stores the initial fetched profile
	const [formIsModified, setFormIsModified] = useState(false); // State to track modification

	const [loading, setLoading] = useState(false);
	const [fetchLoading, setFetchLoading] = useState(true);
	const [message, setMessage] = useState('');
	const [error, setError] = useState('');
	const [activeTab, setActiveTab] = useState('general');

	// For discount rates management
	const [newDiscount, setNewDiscount] = useState({
		minPages: '',
		maxPages: '',
		discount: '',
	});

	// Create a stable reference to the fetch function
	const fetchUserProfile = useRef();

	fetchUserProfile.current = async () => {
		if (!user || !user._id) return;

		try {
			setFetchLoading(true);
			const response = await fetchWithToken(
				`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/users/${user._id}`,
			);

			if (!response.ok) {
				throw new Error('Failed to fetch profile data');
			}

			const userData = await response.json();

			const initial = {
				printingCost: userData.printingCost || '',
				printingLocation: userData.printingLocation || '',
				openingHours: userData.openingHours || '',
				customOpeningHours:
					!PRESET_OPENING_HOURS.includes(userData.openingHours) &&
					userData.openingHours
						? userData.openingHours
						: '',
				discountRates: Array.isArray(userData.discountRates)
					? userData.discountRates
					: [],
				supportContact: userData.supportContact || '',
				additionalInfo: userData.additionalInfo || '',
				rating: userData.rating || 0,
				reviews: userData.reviews || [],
				adminStatus: userData.adminStatus || 'active',
				documentToken: userData.documentToken || 0,
				documentsReceived: userData.documentsReceived || 0,
				queueTimeEstimate: userData.queueTimeEstimate || 0,
			};

			// Store the initial profile data
			setInitialProfile(initial); // Deep copy the initial data

			// Update state with all available user data
			setProfile(initial);

			// If openingHours doesn't match preset options, set it to Custom
			if (
				userData.openingHours &&
				!PRESET_OPENING_HOURS.includes(userData.openingHours)
			) {
				setProfile((prev) => ({
					...prev,
					openingHours: 'Custom',
					customOpeningHours: userData.openingHours,
				}));
			}
		} catch (err) {
			console.error('Error fetching user profile:', err);
			setError('Failed to load profile data. Please refresh the page.');
		} finally {
			setFetchLoading(false);
		}
	};

	useEffect(() => {
		fetchUserProfile.current();
	}, [user]); // Remove presetOpeningHours from dependencies

	const handleChange = (e) => {
		const { name, value } = e.target;
		setProfile({ ...profile, [name]: value });
		setFormIsModified(true);
	};

	const handleNewDiscountChange = (e) => {
		const { name, value } = e.target;
		setNewDiscount({ ...newDiscount, [name]: value });
		setFormIsModified(true);
	};

	// Toggle admin status between active and inactive
	const toggleAdminStatus = () => {
		const newStatus = profile.adminStatus === 'active' ? 'inactive' : 'active';
		setProfile({ ...profile, adminStatus: newStatus });
		setFormIsModified(true);
	};

	const addDiscountRate = () => {
		if (newDiscount.minPages && newDiscount.maxPages && newDiscount.discount) {
			setProfile({
				...profile,
				discountRates: [...profile.discountRates, newDiscount],
			});
			setNewDiscount({ minPages: '', maxPages: '', discount: '' }); // Reset new discount form
			setFormIsModified(true);
		} else {
			setError('Please fill in all fields to add a new discount rate.');
		}
	};

	const removeDiscountRate = (index) => {
		const updatedDiscounts = [...profile.discountRates];
		updatedDiscounts.splice(index, 1);
		setProfile({
			...profile,
			discountRates: updatedDiscounts,
		});
		setFormIsModified(true);
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		setLoading(true);
		setMessage('');
		setError('');

		//Deep compare the profile with the initial profile
		const isProfileChanged =
			JSON.stringify(profile) !== JSON.stringify(initialProfile);

		if (!isProfileChanged && !formIsModified) {
			setError('No changes to save.');
			setLoading(false);
			return; // Don't submit if nothing changed
		}

		const payload = {
			...profile,
			openingHours:
				profile.openingHours === 'Custom'
					? profile.customOpeningHours
					: profile.openingHours,
		};
		try {
			const response = await fetchWithToken(
				`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/users/update-profile`,
				{
					method: 'PUT',
					body: JSON.stringify(payload),
					headers: {
						'Content-Type': 'application/json',
					},
				},
			);

			let data;
			try {
				data = await response.json(); // this can throw if the body is not JSON
			} catch (jsonError) {
				data = {};
			}

			if (response.ok) {
				setMessage('Profile updated successfully');
				setFormIsModified(false); // Reset modification flag
				setTimeout(() => setMessage(''), 5000);
				fetchUserProfile.current(); // Use the ref function
			} else {
				setError(data?.message || 'Error updating profile');
			}
		} catch (error) {
			setError('Failed to update profile');
		} finally {
			setLoading(false);
		}
	};

	const formatDate = (dateString) => {
		const date = new Date(dateString);
		return new Intl.DateTimeFormat('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		}).format(date);
	};

	// Calculate average rating
	const averageRating =
		profile.reviews.length > 0
			? profile.reviews.reduce((sum, review) => sum + review.rating, 0) /
			  profile.reviews.length
			: 0;

	// Render stars for ratings
	const renderStars = (rating) => {
		return (
			<div className='flex'>
				{[1, 2, 3, 4, 5].map((star) => (
					<Star
						key={star}
						size={16}
						className={
							star <= rating
								? 'fill-yellow-400 text-yellow-400'
								: 'text-gray-300'
						}
					/>
				))}
			</div>
		);
	};

	// Get admin status display info
	const getAdminStatusInfo = (status) => {
		switch (status) {
			case 'active':
				return {
					text: 'Active',
					color:
						'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
					icon: (
						<ShieldCheck
							size={14}
							className='mr-1'
						/>
					),
				};
			case 'inactive':
				return {
					text: 'Inactive',
					color: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
					icon: (
						<ShieldX
							size={14}
							className='mr-1'
						/>
					),
				};
			case 'suspended':
				return {
					text: 'Suspended',
					color:
						'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400',
					icon: (
						<Shield
							size={14}
							className='mr-1'
						/>
					),
				};
			default:
				return {
					text: 'Unknown',
					color:
						'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300',
					icon: (
						<Shield
							size={14}
							className='mr-1'
						/>
					),
				};
		}
	};

	const statusInfo = getAdminStatusInfo(profile.adminStatus);

	return (
		<div className='min-h-screen bg-background'>
			<Navbar />

			<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
				{message && (
					<div className='mb-6 bg-green-100 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-400 px-4 py-3 rounded-lg flex items-center justify-between'>
						<p>{message}</p>
						<button
							onClick={() => setMessage('')}
							className='text-green-500 hover:text-green-700 dark:hover:text-green-300'>
							×
						</button>
					</div>
				)}

				{error && (
					<div className='mb-6 bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg flex items-center justify-between'>
						<p>{error}</p>
						<button
							onClick={() => setError('')}
							className='text-red-500 hover:text-red-700 dark:hover:text-red-300'>
							×
						</button>
					</div>
				)}

				<div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
					{/* Profile Summary Card */}
					<div className='lg:col-span-1'>
						<div className='bg-background border border-primary/20 rounded-xl shadow-sm overflow-hidden card-3d'>
							<div className='bg-gradient-to-r from-primary to-accent h-24'></div>
							<div className='px-6 py-6 relative'>
								<div className='absolute -top-10 left-6 bg-background rounded-full p-2 shadow-md'>
									<div className='bg-primary/10 h-16 w-16 rounded-full flex items-center justify-center'>
										<User
											size={32}
											className='text-accent'
										/>
									</div>
								</div>

								<div className='mt-8'>
									<h1 className='text-xl font-bold text-text'>
										{user?.name || 'User'}
									</h1>
									<p className='text-text/70 flex items-center mt-1'>
										<Mail
											size={14}
											className='mr-1 text-primary'
										/>{' '}
										{user?.email || ''}
									</p>

									{user?.isAdmin && (
										<div className='mt-3 flex flex-col gap-2'>
											<div className='flex items-center'>
												<span
													className={`px-2 py-1 text-xs rounded-full flex items-center ${statusInfo.color}`}>
													{statusInfo.icon}
													Admin • {statusInfo.text}
												</span>
												{user?.superAdmin && (
													<span className='ml-2 px-2 py-1 text-xs rounded-full bg-primary/10 text-primary'>
														Super Admin
													</span>
												)}
											</div>

											{/* Admin Status Toggle - Only show if user is admin */}
											<div className='flex items-center justify-between bg-primary/5 p-3 rounded-lg mt-2'>
												<span className='text-sm font-medium text-text'>
													Service Status
												</span>
												<button
													onClick={toggleAdminStatus}
													className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
														profile.adminStatus === 'active'
															? 'bg-green-600'
															: 'bg-red-400'
													}`}
													role='switch'
													aria-checked={profile.adminStatus === 'active'}>
													<span
														className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${
															profile.adminStatus === 'active'
																? 'translate-x-6'
																: 'translate-x-1'
														}`}
													/>
												</button>
											</div>
										</div>
									)}
								</div>

								<div className='mt-4 border-t border-primary/20 pt-4'>
									<div className='flex items-center mb-3'>
										<Award
											size={18}
											className='text-accent mr-2'
										/>
										<span className='text-text/70'>Matric Number:</span>
										<span className='ml-2 font-medium text-text'>
											{user?.matricNumber || '-'}
										</span>
									</div>

									<div className='flex items-center mb-3'>
										<FileText
											size={18}
											className='text-accent mr-2'
										/>
										<span className='text-text/70'>Documents:</span>
										<span className='ml-2 font-medium text-text'>
											{profile.documentsReceived - 1 < 0
												? 0
												: profile.documentsReceived - 1 || 0}
										</span>
									</div>

									<div className='flex items-center'>
										<Tag
											size={18}
											className='text-accent mr-2'
										/>
										<span className='text-text/70'>Tokens:</span>
										<span className='ml-2 font-medium text-text'>
											{profile.documentToken || 0}
										</span>
									</div>
								</div>

								{user?.isAdmin && (
									<div className='mt-4 border-t border-primary/20 pt-4'>
										<div className='flex items-center justify-between mb-2'>
											<span className='text-text font-medium'>Rating</span>
											<div className='flex items-center'>
												<span className='mr-1 font-medium text-text'>
													{profile.rating?.toFixed(1) || 0}
												</span>
												{renderStars(Math.round(profile.rating || 0))}
											</div>
										</div>
										<div className='text-sm text-text/70'>
											Based on {profile.reviews?.length || 0}{' '}
											{profile.reviews?.length === 1 ? 'review' : 'reviews'}
										</div>
									</div>
								)}
							</div>
						</div>
					</div>

					{/* Main Content Area */}
					<div className='lg:col-span-2'>
						<div className='bg-background border border-primary/20 rounded-xl shadow-sm overflow-hidden card-3d'>
							{/* Tabs */}
							<div className='border-b border-primary/20'>
								<nav className='flex -mb-px'>
									<button
										onClick={() => setActiveTab('general')}
										className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
											activeTab === 'general'
												? 'border-primary text-primary'
												: 'border-transparent text-text/70 hover:text-text hover:border-primary/30'
										}`}>
										General Information
									</button>

									{profile.reviews && profile.reviews.length > 0 && (
										<button
											onClick={() => setActiveTab('reviews')}
											className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
												activeTab === 'reviews'
													? 'border-primary text-primary'
													: 'border-transparent text-text/70 hover:text-text hover:border-primary/30'
											}`}>
											Reviews
										</button>
									)}
								</nav>
							</div>

							<div className='p-6'>
								{fetchLoading ? (
									<div className='flex items-center justify-center py-12'>
										<div className='animate-spin rounded-full h-10 w-10 border-b-2 border-primary'></div>
									</div>
								) : (
									<div>
										{/* General Information Tab */}
										{activeTab === 'general' && (
											<form
												onSubmit={handleSubmit}
												className='space-y-6'>
												<div className='flex flex-col md:flex-row gap-4'>
													<div className='md:w-1/2'>
														<label className='block text-sm font-medium text-text mb-1'>
															Name
														</label>
														<input
															type='text'
															value={user?.name || ''}
															className='w-full p-2.5 bg-background border border-primary/30 text-text rounded-lg focus:ring-accent focus:border-primary'
															readOnly
														/>
													</div>
													<div className='md:w-1/2'>
														<label className='block text-sm font-medium text-text mb-1'>
															Email
														</label>
														<input
															type='email'
															value={user?.email || ''}
															className='w-full p-2.5 bg-background border border-primary/30 text-text rounded-lg focus:ring-accent focus:border-primary'
															readOnly
														/>
													</div>
												</div>

												{/* Admin Status Section - Only show for admins */}
												{user?.isAdmin && (
													<div className='bg-primary/5 p-4 rounded-lg border border-primary/20'>
														<h3 className='text-sm font-medium text-text mb-3 flex items-center'>
															<Shield
																size={16}
																className='mr-2 text-primary'
															/>
															Admin Service Settings
														</h3>
														<div className='flex items-center justify-between'>
															<div>
																<p className='text-sm text-text'>
																	Service Status
																</p>
																<p className='text-xs text-text/70'>
																	Toggle your printing service availability
																</p>
															</div>
															<button
																type='button'
																onClick={toggleAdminStatus}
																className={`relative inline-flex h-8 w-14 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
																	profile.adminStatus === 'active'
																		? 'bg-green-600 hover:bg-green-700'
																		: 'bg-red-400 hover:bg-red-500'
																}`}
																role='switch'
																aria-checked={profile.adminStatus === 'active'}>
																<span
																	className={`inline-block h-6 w-6 transform rounded-full bg-white transition-all duration-200 ease-in-out ${
																		profile.adminStatus === 'active'
																			? 'translate-x-7'
																			: 'translate-x-1'
																	}`}
																/>
															</button>
														</div>
														<div className='mt-2'>
															<span
																className={`inline-flex items-center px-2 py-1 text-xs rounded-full ${statusInfo.color}`}>
																{statusInfo.icon}
																Currently {statusInfo.text}
															</span>
														</div>
													</div>
												)}

												<div className='flex flex-col md:flex-row gap-4'>
													<div className='md:w-1/2'>
														<label className='block text-sm font-medium text-text mb-1'>
															Printing Cost
														</label>
														<input
															type='text'
															name='printingCost'
															value={profile.printingCost}
															onChange={handleChange}
															className='w-full p-2.5 bg-background border border-primary/30 text-text rounded-lg focus:ring-accent focus:border-primary'
														/>
													</div>
													<div className='md:w-1/2'>
														<label className='block text-sm font-medium text-text mb-1'>
															Printing Location
														</label>
														<Autocomplete
															apiKey={
																process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
															}
															className='w-full p-2.5 bg-background border border-primary/30 text-text rounded-lg focus:ring-accent focus:border-primary'
															onPlaceSelected={(place) => {
																setProfile({
																	...profile,
																	printingLocation: place.formatted_address,
																});
																setFormIsModified(true);
															}}
															defaultValue={profile.printingLocation}
															options={{
																types: ['geocode'],
															}}
														/>
													</div>
												</div>
												<div className='flex flex-col md:flex-row gap-4'>
													<div className='md:w-1/2'>
														<label className='block text-sm font-medium text-text mb-1'>
															Opening Hours
														</label>
														<select
															name='openingHours'
															value={profile.openingHours}
															onChange={handleChange}
															className='w-full p-2.5 bg-background border border-primary/30 text-text rounded-lg focus:ring-accent focus:border-primary'>
															{PRESET_OPENING_HOURS.map((hours, index) => (
																<option
																	key={index}
																	value={hours}>
																	{hours}
																</option>
															))}
														</select>
													</div>
													{profile.openingHours === 'Custom' && (
														<div className='md:w-1/2'>
															<label className='block text-sm font-medium text-text mb-1'>
																Custom Opening Hours
															</label>
															<input
																type='text'
																name='customOpeningHours'
																value={profile.customOpeningHours}
																onChange={handleChange}
																className='w-full p-2.5 bg-background border border-primary/30 text-text rounded-lg focus:ring-accent focus:border-primary'
															/>
														</div>
													)}
												</div>
												<div className='flex flex-col md:flex-row gap-4'>
													<div className='md:w-1/2'>
														<label className='block text-sm font-medium text-text mb-1'>
															Support Contact
														</label>
														<input
															type='text'
															name='supportContact'
															value={profile.supportContact}
															onChange={handleChange}
															className='w-full p-2.5 bg-background border border-primary/30 text-text rounded-lg focus:ring-accent focus:border-primary'
														/>
													</div>
													<div className='md:w-1/2'>
														<label className='block text-sm font-medium text-text mb-1'>
															Additional Information
														</label>
														<input
															type='text'
															name='additionalInfo'
															value={profile.additionalInfo}
															onChange={handleChange}
															className='w-full p-2.5 bg-background border border-primary/30 text-text rounded-lg focus:ring-accent focus:border-primary'
														/>
													</div>
												</div>
												<div className='space-y-4'>
													<label className='block text-sm font-medium text-text mb-1'>
														Discount Rates
													</label>
													<div className='flex flex-col gap-2'>
														<label className='block text-sm font-medium text-text mb-1'>
															Min Pages
														</label>
														<input
															type='number'
															name='minPages'
															value={newDiscount.minPages}
															onChange={handleNewDiscountChange}
															className='w-full p-2.5 bg-background border border-primary/30 text-text rounded-lg focus:ring-accent focus:border-primary'
															placeholder='Minimum Pages'
														/>
														<label className='block text-sm font-medium text-text mb-1'>
															Max Pages
														</label>
														<input
															type='number'
															name='maxPages'
															value={newDiscount.maxPages}
															onChange={handleNewDiscountChange}
															className='w-full p-2.5 bg-background border border-primary/30 text-text rounded-lg focus:ring-accent focus:border-primary'
															placeholder='Maximum Pages'
														/>
														<label className='block text-sm font-medium text-text mb-1'>
															Discount (%)
														</label>
														<input
															type='number'
															name='discount'
															value={newDiscount.discount}
															onChange={handleNewDiscountChange}
															className='w-full p-2.5 bg-background border border-primary/30 text-text rounded-lg focus:ring-accent focus:border-primary'
															placeholder='Discount Percentage'
														/>
														<button
															type='button'
															onClick={addDiscountRate}
															className='p-2.5 bg-gradient-to-r from-primary to-accent text-white rounded-lg hover:opacity-90 transition-colors btn-3d'>
															Add Discount Rate
														</button>
													</div>
													<div className='flex flex-wrap gap-2'>
														{profile.discountRates.map((discount, index) => (
															<div
																key={index}
																className='flex items-center bg-primary/10 rounded-full px-3 py-1 text-sm text-text'>
																<span>
																	{discount.minPages}-{discount.maxPages}:{' '}
																	{discount.discount}%
																</span>
																<button
																	type='button'
																	onClick={() => removeDiscountRate(index)}
																	className='ml-2 text-primary hover:text-accent'>
																	×
																</button>
															</div>
														))}
													</div>
												</div>
												<div className='flex justify-end'>
													<button
														type='submit'
														disabled={loading || !formIsModified}
														className='px-6 py-2.5 bg-gradient-to-r from-primary to-accent text-white rounded-lg hover:opacity-90 transition-colors disabled:opacity-50 btn-3d'>
														{loading ? 'Saving...' : 'Save Changes'}
													</button>
												</div>
											</form>
										)}

										{/* Reviews Tab */}
										{activeTab === 'reviews' && profile.reviews.length > 0 && (
											<div className='space-y-6'>
												<div className='flex items-center justify-between'>
													<h3 className='text-lg font-semibold text-primary'>
														Reviews
													</h3>
													<div className='flex items-center'>
														<span className='mr-2 font-medium text-text'>
															{averageRating.toFixed(1)}
														</span>
														{renderStars(Math.round(averageRating))}
													</div>
												</div>
												<div className='space-y-4'>
													{profile.reviews.map((review, index) => (
														<div
															key={index}
															className='bg-primary/5 p-4 rounded-lg'>
															<div className='flex items-center justify-between'>
																<div className='flex items-center'>
																	<span className='font-medium text-text'>
																		{review.name || 'Anonymous'}
																	</span>
																	<span className='text-sm text-text/70 ml-2'>
																		{/* {formatDate(review.date)} */}
																	</span>
																</div>
																{renderStars(review.rating)}
															</div>
															<p className='mt-2 text-text'>{review.comment}</p>
														</div>
													))}
												</div>
											</div>
										)}
									</div>
								)}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default ProfilePage;
