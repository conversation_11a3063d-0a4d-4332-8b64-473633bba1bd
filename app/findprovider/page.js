'use client';
import React, { useState, useEffect, useContext, Suspense } from 'react';
import { useRouter } from 'next/navigation';
import { AuthContext } from '../context/AuthContext';
import { useData } from '../context/DataContext';
import Navbar from '@/components/Navbar';
import SearchAndFilter from '@/components/FindProvider/SearchAndFilter';
import SkeletonLoader from '@/components/FindProvider/SkeletonLoader';
import ErrorComponent from '@/components/ErrorComponent';
import ProviderCard from '@/components/FindProvider/ProviderCard';
import NoResultsComponent from '@/components/FindProvider/NoResultsComponent';
import Pagination from '@/components/Pagination';

const FindProvider = () => {
	const router = useRouter();
	const {
		adminUsers,
		loading,
		error,
		fetchData,
		pagination,
		updatePagination,
	} = useData();
	const { user } = useContext(AuthContext);

	const [searchQuery, setSearchQuery] = useState('');
	const [filters, setFilters] = useState({ location: '', priceRange: '' });
	const [showFilters, setShowFilters] = useState(false);
	const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
	const [debouncedFilters, setDebouncedFilters] = useState({});

	// Debounce search query
	useEffect(() => {
		const handler = setTimeout(() => {
			setDebouncedSearchQuery(searchQuery);
		}, 500);
		return () => {
			clearTimeout(handler);
		};
	}, [searchQuery]);

	// Debounce filters
	useEffect(() => {
		const handler = setTimeout(() => {
			setDebouncedFilters(filters);
		}, 500);
		return () => {
			clearTimeout(handler);
		};
	}, [filters]);

	// Fetch data when debounced values change
	useEffect(() => {
		const searchParams = {};
		let combinedQuery = '';

		// Build the combined search query
		// The backend now searches across name, printingLocation, and supportContact
		if (debouncedSearchQuery) {
			combinedQuery = debouncedSearchQuery.trim();
		}

		// Add location filter to the search query if it's different from main search
		if (
			debouncedFilters.location &&
			debouncedFilters.location.trim().toLowerCase() !==
				combinedQuery.toLowerCase()
		) {
			if (combinedQuery) {
				combinedQuery += ' ';
			}
			combinedQuery += debouncedFilters.location.trim();
		}

		// Send the query parameter to backend (searches name, printingLocation, supportContact)
		if (combinedQuery) {
			searchParams.query = combinedQuery;
		}

		// Reset to first page when search parameters change
		updatePagination('admins', { currentPage: 1 }, searchParams);
	}, [debouncedSearchQuery, debouncedFilters.location, updatePagination]);

	const handleRetry = () => {
		fetchData('admins', true);
	};

	const resetFilters = () => {
		setFilters({ location: '', priceRange: '' });
		setSearchQuery('');
	};

	const handleRatingUpdated = (adminId, newRating) => {
		if (!user || !user._id) {
			alert('You must be logged in to rate an admin.');
			return;
		}
		if (user._id === adminId) {
			alert('You cannot rate yourself.');
			return;
		}

		// Could implement optimistic UI update here if needed
		return user.name;
	};

	const handleAdminSelect = (admin) => {
		router.push(`/submit?adminId=${admin._id}`);
	};

	const handlePageChange = (newPage) => {
		// Maintain current search when changing pages
		const searchParams = {};
		let combinedQuery = '';

		if (debouncedSearchQuery) {
			combinedQuery = debouncedSearchQuery.trim();
		}

		if (
			debouncedFilters.location &&
			debouncedFilters.location.trim().toLowerCase() !==
				combinedQuery.toLowerCase()
		) {
			if (combinedQuery) {
				combinedQuery += ' ';
			}
			combinedQuery += debouncedFilters.location.trim();
		}

		if (combinedQuery) {
			searchParams.query = combinedQuery;
		}

		updatePagination('admins', { currentPage: newPage }, searchParams);
	};

	const handleLimitChange = (newLimit) => {
		// Maintain current search when changing page size
		const searchParams = {};
		let combinedQuery = '';

		if (debouncedSearchQuery) {
			combinedQuery = debouncedSearchQuery.trim();
		}

		if (
			debouncedFilters.location &&
			debouncedFilters.location.trim().toLowerCase() !==
				combinedQuery.toLowerCase()
		) {
			if (combinedQuery) {
				combinedQuery += ' ';
			}
			combinedQuery += debouncedFilters.location.trim();
		}

		if (combinedQuery) {
			searchParams.query = combinedQuery;
		}

		updatePagination(
			'admins',
			{ limit: newLimit, currentPage: 1 },
			searchParams,
		);
	};

	return (
		<div>
			<Navbar />
			<div className='min-h-screen bg-background py-12 px-4'>
				<div className='max-w-6xl mx-auto'>
					<header className='mb-10 text-center'>
						<h1 className='text-4xl font-bold text-text mb-4'>Find a Vendor</h1>
						<p className='text-text/80 text-lg max-w-2xl mx-auto'>
							Connect with trusted vendors in your area. Search by name,
							location, or contact information.
						</p>
					</header>

					<SearchAndFilter
						searchQuery={searchQuery}
						setSearchQuery={setSearchQuery}
						filters={filters}
						setFilters={setFilters}
						showFilters={showFilters}
						setShowFilters={setShowFilters}
						resetFilters={resetFilters}
					/>

					{loading.admins ? (
						<SkeletonLoader />
					) : error.admins ? (
						<ErrorComponent
							error={error.admins}
							handleRetry={handleRetry}
						/>
					) : (
						<>
							{adminUsers.length > 0 ? (
								<>
									<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
										{adminUsers.map((admin) => (
											<ProviderCard
												key={admin._id}
												admin={admin}
												handleAdminSelect={handleAdminSelect}
												handleRatingUpdated={handleRatingUpdated}
											/>
										))}
									</div>

									<Pagination
										pagination={pagination.admins}
										onPageChange={handlePageChange}
										onLimitChange={handleLimitChange}
										itemName='vendors'
										currentItems={adminUsers}
									/>
								</>
							) : (
								<NoResultsComponent
									searchQuery={searchQuery}
									setSearchQuery={setSearchQuery}
								/>
							)}
						</>
					)}
				</div>
			</div>
		</div>
	);
};

// Wrap the component in Suspense
const FindProviderPage = () => {
	return (
		<Suspense
			fallback={
				<div className='min-h-screen flex items-center justify-center bg-background'>
					<div className='animate-spin text-primary'>
						<svg
							className='w-10 h-10'
							xmlns='http://www.w3.org/2000/svg'
							fill='none'
							viewBox='0 0 24 24'>
							<circle
								className='opacity-25'
								cx='12'
								cy='12'
								r='10'
								stroke='currentColor'
								strokeWidth='4'></circle>
							<path
								className='opacity-75'
								fill='currentColor'
								d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'></path>
						</svg>
					</div>
				</div>
			}>
			<FindProvider />
		</Suspense>
	);
};

export default FindProviderPage;
