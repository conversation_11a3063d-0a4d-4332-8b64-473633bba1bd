'use client';
import { useState, useContext, useEffect } from 'react';
import { AuthContext } from '@/app/context/AuthContext';
import {
	<PERSON>a<PERSON><PERSON><PERSON>,
	FaCheckCircle,
	FaExclamationCircle,
	FaEye,
	FaEyeSlash,
} from 'react-icons/fa';
import { Alert, AlertDescription } from '@/components/ui/alert';
import Navbar from '@/components/Navbar';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

const ForgotPassword = () => {
	const { forgotPassword, resetPassword } = useContext(AuthContext);
	const [email, setEmail] = useState('');
	const [otp, setOtp] = useState('');
	const [newPassword, setNewPassword] = useState('');
	const [showPassword, setShowPassword] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [notification, setNotification] = useState({ type: '', message: '' });
	const [step, setStep] = useState(1); // 1: Request OTP, 2: Verify OTP and Reset Password
	const router = useRouter();

	const handleEmailChange = (e) => {
		setEmail(e.target.value);
	};

	const handleOtpChange = (e) => {
		setOtp(e.target.value);
	};

	const handleNewPasswordChange = (e) => {
		setNewPassword(e.target.value);
	};

	const togglePasswordVisibility = () => {
		setShowPassword(!showPassword);
	};

	const showNotification = (type, message) => {
		setNotification({ type, message });
		setTimeout(() => setNotification({ type: '', message: '' }), 10000);
	};

	const handleRequestOTP = async (e) => {
		e.preventDefault();
		setIsLoading(true);
		// console.log('OTP Request Sent');

		try {
			const result = await forgotPassword(email);
			if (result.success) {
				showNotification('success', result.message);
				setStep(2); // Move to OTP step
			} else {
				showNotification('error', result.message);
			}
		} catch (error) {
			showNotification('error', 'Failed to send OTP. Please try again.');
		} finally {
			setIsLoading(false);
		}
	};

	const handleResetPassword = async (e) => {
		e.preventDefault();
		setIsLoading(true);

		try {
			const result = await resetPassword(email, otp, newPassword);

			if (result.success) {
				showNotification('success', result.message);
				setTimeout(() => {
					router.push('/auth/login');
				}, 2000);
			} else {
				showNotification('error', result.message);
			}
		} catch (error) {
			showNotification('error', 'Failed to reset password. Please try again.');
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div>
			<Navbar />
			<div className='min-h-screen flex flex-col items-center justify-center bg-background text-text p-4'>
				<div className='card backdrop-blur-md p-8 rounded-lg shadow-lg w-96 max-w-full'>
					<h2 className='text-3xl font-bold mb-6 text-center'>
						{step === 1 ? 'Forgot Password' : 'Reset Password'}
					</h2>

					{notification.message && (
						<Alert
							className={`mb-6 ${
								notification.type === 'success'
									? 'bg-green-500 bg-opacity-20 border-green-500'
									: 'bg-red-500 bg-opacity-20 border-red-500'
							}`}>
							{notification.type === 'success' ? (
								<FaCheckCircle className='h-4 w-4' />
							) : (
								<FaExclamationCircle className='h-4 w-4' />
							)}
							<AlertDescription>{notification.message}</AlertDescription>
						</Alert>
					)}

					{step === 1 ? (
						<form
							onSubmit={handleRequestOTP}
							className='space-y-4'>
							<input
								type='email'
								name='email'
								placeholder='Enter your email'
								value={email}
								onChange={handleEmailChange}
								className='w-full px-4 py-2 rounded-lg bg-background text-text focus:outline-none focus:ring-2 focus:ring-purple-400'
								required
								disabled={isLoading}
							/>
							<button
								type='submit'
								disabled={isLoading}
								className='w-full bg-primary hover:bg-accent text-text font-bold py-2 rounded-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center'>
								{isLoading ? (
									<>
										<FaSpinner className='animate-spin mr-2 h-5 w-5' />
										Sending OTP...
									</>
								) : (
									'Send OTP'
								)}
							</button>
						</form>
					) : (
						<form
							onSubmit={handleResetPassword}
							className='space-y-4'
							autoComplete='off'>
							<input
								type='text'
								name='otp'
								placeholder='Enter OTP'
								value={otp}
								onChange={handleOtpChange}
								className='w-full px-4 py-2 rounded-lg bg-gray-800 text-white focus:outline-none focus:ring-2 focus:ring-purple-400'
								required
								autoComplete='off'
								disabled={isLoading}
							/>
							<div className='relative'>
								<input
									type={showPassword ? 'text' : 'password'}
									name='newPassword'
									placeholder='Enter new password'
									value={newPassword}
									onChange={handleNewPasswordChange}
									className='w-full px-4 py-2 rounded-lg bg-gray-800 text-white focus:outline-none focus:ring-2 focus:ring-purple-400'
									required
									disabled={isLoading}
								/>
								<button
									type='button'
									onClick={togglePasswordVisibility}
									className='absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5'>
									{showPassword ? (
										<FaEyeSlash className='h-5 w-5 text-gray-400' />
									) : (
										<FaEye className='h-5 w-5 text-gray-400' />
									)}
								</button>
							</div>
							<button
								type='submit'
								disabled={isLoading}
								className='w-full bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 rounded-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center'>
								{isLoading ? (
									<>
										<FaSpinner className='animate-spin mr-2 h-5 w-5' />
										Resetting Password...
									</>
								) : (
									'Reset Password'
								)}
							</button>
						</form>
					)}

					<p className='mt-4 text-center'>
						Remember your password?{' '}
						<Link
							href='/auth/login'
							className='text-primary hover:underline'>
							Login
						</Link>
					</p>
				</div>
			</div>
		</div>
	);
};

export default ForgotPassword;
