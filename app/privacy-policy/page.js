import Navbar from '@/components/Navbar';
import React from 'react';

const PrivacyPolicy = () => {
	return (
		<div>
			<Navbar />
			<div className='container mx-auto px-4 py-8'>
				<h1 className='text-3xl font-bold mb-4'>
					Privacy Policy for uploaddocs
				</h1>
				<p className='text-gray-600'>Effective Date: 10/02/2025</p>

				<section className='mt-6'>
					<h2 className='text-2xl font-semibold'>1. Introduction</h2>
					<p>
						Welcome to uploaddocs! This Privacy Policy explains how we collect,
						use, and protect your information when you use our platform for
						document sharing with printing vendors/admins. We are committed to
						protecting your privacy and handling your data responsibly.
					</p>
				</section>

				<section className='mt-6'>
					<h2 className='text-2xl font-semibold'>2. Information We Collect</h2>
					<ul className='list-disc list-inside'>
						<li>
							<strong>Email Address:</strong> We collect your email address for
							account verification and communication purposes related to your
							document submissions. We will not use your email for marketing
							without your explicit consent.
						</li>
						<li>
							<strong>Uploaded Documents:</strong> You upload documents to our
							platform for sharing with your chosen printing vendor/admin. We
							store these documents temporarily to facilitate this sharing
							process.
						</li>
					</ul>
				</section>

				<section className='mt-6'>
					<h2 className='text-2xl font-semibold'>
						3. How We Use Your Information
					</h2>
					<ul className='list-disc list-inside'>
						<li>
							<strong>Account Verification:</strong> Your email address is used
							to verify your identity and ensure secure access to your account.
						</li>
						<li>
							<strong>Document Sharing:</strong> Your uploaded documents are
							shared with the printing vendor/admin you select. We do not access
							or process the content beyond what is necessary.
						</li>
						<li>
							<strong>Communication:</strong> We may use your email to send
							notifications related to document submissions.
						</li>
					</ul>
				</section>

				<section className='mt-6'>
					<h2 className='text-2xl font-semibold'>
						4. Data Retention and Deletion
					</h2>
					<ul className='list-disc list-inside'>
						<li>
							<strong>Document Retention:</strong> Documents are retained only
							as long as necessary for sharing with the chosen vendor/admin.
						</li>
						<li>
							<strong>User-Initiated Deletion:</strong> You can delete any
							document at any time, and it will be permanently removed from our
							database.
						</li>
						<li>
							<strong>Email Retention:</strong> We retain your email as long as
							your account is active. Account deletion results in email removal.
						</li>
					</ul>
				</section>

				<section className='mt-6'>
					<h2 className='text-2xl font-semibold'>5. Data Security</h2>
					<p>
						We take reasonable measures to protect your information from
						unauthorized access, use, or disclosure, including access controls.
						However, no method is 100% secure.
					</p>
				</section>

				<section className='mt-6'>
					<h2 className='text-2xl font-semibold'>
						6. Sharing Your Information
					</h2>
					<ul className='list-disc list-inside'>
						<li>
							<strong>Printing Vendors/Admins:</strong> Your uploaded documents
							are shared with the printing vendor/admin you select. We do not
							share your email unless necessary.
						</li>
						<li>
							<strong>Legal Authorities:</strong> We may disclose information if
							required by law.
						</li>
					</ul>
				</section>

				<section className='mt-6'>
					<h2 className='text-2xl font-semibold'>7. Children's Privacy</h2>
					<p>
						Our platform is not intended for children under 12 years of age. If
						you believe a child has provided us personal information, please
						contact us.
					</p>
				</section>

				<section className='mt-6'>
					<h2 className='text-2xl font-semibold'>
						8. Changes to this Privacy Policy
					</h2>
					<p>
						We may update this Privacy Policy periodically. Continued use of the
						platform after updates constitutes acceptance of the changes.
					</p>
				</section>

				<section className='mt-6'>
					<h2 className='text-2xl font-semibold'>9. Contact Us</h2>
					<p>
						If you have any questions, contact <NAME_EMAIL>.
					</p>
				</section>

				<section className='mt-6'>
					<h2 className='text-2xl font-semibold'>10. Governing Law</h2>
					<p>This Privacy Policy is governed by the laws of Supreme Court .</p>
				</section>
			</div>
		</div>
	);
};

export default PrivacyPolicy;
