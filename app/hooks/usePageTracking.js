'use client';

import { useEffect } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { useAnalytics } from '../context/AnalyticsContext';

/**
 * Hook to track page views in Firebase Analytics
 * @param {string} pageName - Optional custom page name, defaults to pathname
 * @param {object} additionalParams - Optional additional parameters to include with the page_view event
 */
export const usePageTracking = (pageName, additionalParams = {}) => {
	const pathname = usePathname();
	const searchParams = useSearchParams();
	const { trackPageView } = useAnalytics();

	useEffect(() => {
		// Use provided pageName or extract from pathname
		const pageTitle = pageName || pathname.split('/').pop() || 'home';

		// Track the page view
		trackPageView(pageTitle, {
			...additionalParams,
			has_query_params: searchParams ? searchParams.size > 0 : false,
			path: pathname,
		});
	}, [pathname, searchParams, trackPageView, pageName, additionalParams]);
};

/**
 * Safe version of usePageTracking that doesn't use useSearchParams
 * Use this in components that don't need to be wrapped in Suspense
 */
export const useSafePageTracking = (pageName, additionalParams = {}) => {
	const pathname = usePathname();
	const { trackPageView } = useAnalytics();

	useEffect(() => {
		// Use provided pageName or extract from pathname
		const pageTitle = pageName || pathname.split('/').pop() || 'home';

		// Track the page view without search params
		trackPageView(pageTitle, {
			...additionalParams,
			path: pathname,
		});
	}, [pathname, trackPageView, pageName, additionalParams]);
};

export default usePageTracking;
