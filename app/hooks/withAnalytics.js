'use client';

import { useEffect } from 'react';
import { useAnalytics } from '../context/AnalyticsContext';

/**
 * Higher-order component that adds analytics tracking to a page component
 * @param {React.Component} Component - The component to wrap
 * @param {string} pageName - The name of the page for analytics
 * @param {object} pageParams - Additional parameters to include with the page_view event
 * @returns {React.Component} - The wrapped component with analytics
 */
export const withAnalytics = (Component, pageName, pageParams = {}) => {
  // Return a new component with the same props
  return function AnalyticsWrapper(props) {
    const { trackPageView, trackEvent } = useAnalytics();
    
    useEffect(() => {
      // Track page view when component mounts
      trackPageView(pageName, pageParams);
      
      // Track component mount event
      trackEvent('component_mounted', {
        component_name: pageName,
        ...pageParams
      });
      
      // Return cleanup function
      return () => {
        // Track component unmount event
        trackEvent('component_unmounted', {
          component_name: pageName,
          ...pageParams
        });
      };
    }, [trackPageView, trackEvent]);
    
    // Render the original component with all props
    return <Component {...props} />;
  };
};

export default withAnalytics;
