'use client';

import Link from 'next/link';
import { useEffect } from 'react';
import { FaExclamationTriangle, FaHome, FaArrowLeft } from 'react-icons/fa';
import Navbar from '@/components/Navbar';
import { useAnalytics } from './context/AnalyticsContext';

export default function NotFound() {
  const { trackEvent } = useAnalytics();
  
  useEffect(() => {
    // Track 404 page view
    if (trackEvent) {
      trackEvent('404_page_view', {
        path: typeof window !== 'undefined' ? window.location.pathname : '',
        referrer: typeof document !== 'undefined' ? document.referrer : ''
      });
    }
  }, [trackEvent]);

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <div className="flex flex-col items-center justify-center px-6 py-24 md:py-32">
        <div className="relative">
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-10 left-10 w-40 h-40 rounded-full bg-primary blur-3xl"></div>
            <div className="absolute bottom-10 right-10 w-60 h-60 rounded-full bg-secondary blur-3xl"></div>
          </div>
          
          <div className="relative z-10 max-w-md w-full backdrop-blur-md p-8 rounded-xl shadow-lg card text-center">
            <div className="flex justify-center mb-6">
              <div className="btn-3d bg-gradient-to-r from-primary to-accent p-4 rounded-full">
                <FaExclamationTriangle className="w-8 h-8 text-white" />
              </div>
            </div>
            
            <h1 className="text-4xl font-bold mb-2 text-text">404</h1>
            <h2 className="text-2xl font-semibold mb-4 text-text">Page Not Found</h2>
            
            <p className="text-text/80 mb-8">
              The page you're looking for doesn't exist or has been moved.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/"
                className="btn-3d bg-gradient-to-r from-primary to-accent text-white px-6 py-3 rounded-lg flex items-center justify-center gap-2 hover:opacity-90 transition-all"
              >
                <FaHome className="w-4 h-4" />
                <span>Go Home</span>
              </Link>
              
              <button 
                onClick={() => window.history.back()}
                className="glow-effect bg-primary/10 text-text px-6 py-3 rounded-lg hover:bg-primary/20 transition-all flex items-center justify-center gap-2"
              >
                <FaArrowLeft className="w-4 h-4" />
                <span>Go Back</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
