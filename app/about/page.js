'use client';
import Navbar from '@/components/Navbar';
import { FaWhatsapp, FaGlobe } from 'react-icons/fa';

export default function AboutPage() {
	return (
		<div className='min-h-screen bg-background flex flex-col'>
			<Navbar />
			<div className='flex-1 flex items-center justify-center p-6'>
				<div className='max-w-5xl w-full bg-primary/5 backdrop-blur-md rounded-3xl shadow-2xl p-10 space-y-10 text-text card'>
					<header className='text-center'>
						<h1 className='text-5xl font-extrabold mb-2'>
							About <span className='text-accent'>UploadDoc</span>
						</h1>
						<p className='text-text/80 mt-2 text-lg'>
							Streamlining student project submissions effortlessly.
						</p>
					</header>

					<section>
						<h2 className='text-3xl font-bold mb-4 text-secondary'>
							Our Mission
						</h2>
						<p className='text-text/90 text-lg'>
							UploadDoc simplifies the way students submit projects and how
							administrators manage them. Our goal is to create a frictionless
							and efficient workflow.
						</p>
					</section>

					<section>
						<h2 className='text-3xl font-bold mb-4 text-secondary'>
							Why UploadDoc?
						</h2>
						<p className='text-text/90 text-lg'>
							Created by <strong>Daniel Agbeni</strong>, UploadDoc makes it
							easier to collect documents for academic needs, while ensuring
							secure, verified transactions through email verification.
						</p>
					</section>

					<section>
						<h2 className='text-3xl font-bold mb-4 text-secondary'>
							Key Features
						</h2>
						<ul className='list-disc list-inside space-y-2 text-text/90 text-lg'>
							<li>Simple and fast document uploads</li>
							<li>Secure cloud file management</li>
							<li>Admin dashboard for oversight</li>
							<li>Streamlined verification process</li>
							<li>Modern, responsive user experience</li>
						</ul>
					</section>

					<section>
						<h2 className='text-3xl font-bold mb-4 text-secondary'>
							Contact Us
						</h2>
						<p className='text-text/90 text-lg'>
							Questions? Reach out via email at{' '}
							<a
								href='mailto:<EMAIL>'
								className='text-accent hover:underline hover:text-secondary'>
								<EMAIL>
							</a>
						</p>

						<div className='flex space-x-6 mt-4 text-3xl'>
							<a
								href='https://wa.me/+2349041995875'
								target='_blank'
								title='Message Daniel Agbeni On Whatsapp'
								rel='noopener noreferrer'
								className='hover:text-green-500 text-text/90'>
								<FaWhatsapp />
							</a>
							<a
								href='https://danielagbeni.netlify.app/'
								target='_blank'
								title='Visit Daniel Agbeni Website'
								rel='noopener noreferrer'
								className='hover:text-blue-500 text-text/90 hover:text-accent'>
								<FaGlobe />
							</a>
						</div>
					</section>
				</div>
			</div>
		</div>
	);
}
