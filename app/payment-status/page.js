'use client';
import { useContext, useEffect, useState, Suspense } from 'react';
import {
	FaCheckCircle,
	FaExclamationCircle,
	FaSpinner,
	FaCreditCard,
	FaUserShield,
} from 'react-icons/fa';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
	Card,
	CardHeader,
	CardTitle,
	CardDescription,
	CardContent,
} from '@/components/ui/card';
import Navbar from '@/components/Navbar';
import { useRouter } from 'next/navigation';
import { AuthContext } from '../context/AuthContext';

const PaymentStatus = () => {
	const [notification, setNotification] = useState({ type: '', message: '' });
	const [purchaseDetails, setPurchaseDetails] = useState(null);
	const [timeLeft, setTimeLeft] = useState(8);
	const router = useRouter();
	const { logout } = useContext(AuthContext);

	useEffect(() => {
		const queryParams = new URLSearchParams(window.location.search);
		const status = queryParams.get('status');
		const message = queryParams.get('message');
		const tokens = queryParams.get('tokens');
		const amount = queryParams.get('amount');

		const statusMessages = {
			success: 'Payment verified and you are now an admin!',
			already_admin: 'You are already an admin.',
			failed: message || 'Payment failed.',
			error: message || 'An error occurred during verification.',
		};

		if (status) {
			setNotification({
				type: status === 'already_admin' ? 'info' : status,
				message: statusMessages[status],
			});

			if (status === 'success' && tokens && amount) {
				setPurchaseDetails({
					tokens: parseInt(tokens),
					amount: parseFloat(amount),
				});
			}
		}

		// Countdown timer
		const timer = setInterval(() => {
			setTimeLeft((prev) => Math.max(0, prev - 1));
		}, 1000);

		// Logout timeout
		const logoutTimeout = setTimeout(() => {
			logout();
			router.push('/auth/login');
		}, 8000);

		return () => {
			clearInterval(timer);
			clearTimeout(logoutTimeout);
		};
	}, [logout, router]);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Navbar />
			<div className='max-w-4xl mx-auto px-4 py-12'>
				<div className='text-center mb-8'>
					<h1 className='text-3xl font-bold text-gray-900'>Payment Status</h1>
					<p className='text-gray-600 mt-2'>
						Redirecting to login in {timeLeft} seconds...
					</p>
				</div>

				<Card className='max-w-lg mx-auto'>
					<CardHeader className='text-center'>
						{notification.type === 'success' ? (
							<div className='mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4'>
								<FaCheckCircle className='h-8 w-8 text-green-500' />
							</div>
						) : notification.type === 'error' ? (
							<div className='mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4'>
								<FaExclamationCircle className='h-8 w-8 text-red-500' />
							</div>
						) : (
							<div className='mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4'>
								<FaSpinner className='h-8 w-8 text-blue-500 animate-spin' />
							</div>
						)}
						<CardTitle className='text-2xl font-bold'>
							{notification.type === 'success'
								? 'Payment Successful'
								: notification.type === 'error'
								? 'Payment Failed'
								: 'Processing Payment'}
						</CardTitle>
					</CardHeader>

					<CardContent className='space-y-6'>
						{notification.message && (
							<Alert
								className={`${
									notification.type === 'success'
										? 'bg-green-50 border-green-200 text-green-800'
										: notification.type === 'error'
										? 'bg-red-50 border-red-200 text-red-800'
										: 'bg-blue-50 border-blue-200 text-blue-800'
								}`}>
								<AlertDescription>{notification.message}</AlertDescription>
							</Alert>
						)}

						{purchaseDetails && (
							<div className='space-y-4'>
								<div className='bg-gray-50 p-4 rounded-lg'>
									<div className='flex items-center justify-between mb-2'>
										<div className='flex items-center gap-2'>
											<FaCreditCard className='text-blue-500' />
											<span className='text-gray-600'>Amount Paid</span>
										</div>
										<span className='font-semibold text-gray-900'>
											₦{purchaseDetails.amount.toLocaleString()}
										</span>
									</div>
									<div className='flex items-center justify-between'>
										<div className='flex items-center gap-2'>
											<FaUserShield className='text-blue-500' />
											<span className='text-gray-600'>Tokens Purchased</span>
										</div>
										<span className='font-semibold text-gray-900'>
											{purchaseDetails.tokens.toLocaleString()} tokens
										</span>
									</div>
								</div>
							</div>
						)}

						<div className='text-center text-sm text-gray-500'>
							<FaSpinner className='inline animate-spin mr-2' />
							Logging out and redirecting to login page...
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
};

// Wrap the component in Suspense for URLSearchParams
const PaymentStatusPage = () => {
	return (
		<Suspense
			fallback={
				<div className='min-h-screen flex items-center justify-center bg-gray-50'>
					<FaSpinner className='animate-spin h-10 w-10 text-blue-500' />
				</div>
			}>
			<PaymentStatus />
		</Suspense>
	);
};

export default PaymentStatusPage;
