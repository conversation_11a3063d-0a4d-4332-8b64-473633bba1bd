'use client';
import { useState, useContext, useEffect, Suspense } from 'react';
import { AuthContext } from '@/app/context/AuthContext';
import {
	<PERSON>a<PERSON><PERSON>ner,
	FaCheckCircle,
	FaExclamationCircle,
	FaCrown,
	FaShieldAlt,
} from 'react-icons/fa';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
	Card,
	CardHeader,
	CardTitle,
	CardDescription,
	CardContent,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
	Dialog,
	DialogTrigger,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogDescription,
	DialogFooter,
	DialogClose,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import Navbar from '@/components/Navbar';
import Link from 'next/link';

const PaymentAndPromotion = () => {
	const { fetchWithToken, user } = useContext(AuthContext);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [notification, setNotification] = useState({ type: '', message: '' });
	const [paymentUrl, setPaymentUrl] = useState('');
	const [selectedPackage, setSelectedPackage] = useState(null);

	const showNotification = (type, message) => {
		setNotification({ type, message });
		setTimeout(() => setNotification({ type: '', message: '' }), 5000);
	};

	const handlePayment = async () => {
		if (!selectedPackage) {
			showNotification('error', 'Please select a package.');
			return;
		}

		setIsSubmitting(true);
		try {
			const response = await fetchWithToken(
				`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/payment/initialize`,
				{
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({
						userId: user._id,
						amount: Number(selectedPackage.amount),
						package: selectedPackage.id,
					}),
				},
			);

			const data = await response.json();
			if (!response.ok)
				throw new Error(data.error || 'Payment initialization failed.');

			if (data.status === 'success') {
				// Handle free basic package (direct response without payment gateway)
				if (selectedPackage.id === 1 && selectedPackage.amount === 0) {
					if (data.data?.redirectUrl) {
						window.location.href = data.data.redirectUrl;
					} else {
						// Fallback for success without redirect URL
						showNotification(
							'success',
							'Basic package activated successfully!',
						);
						// Refresh the page after a short delay
						setTimeout(() => window.location.reload(), 2000);
					}
				}
				// Handle paid packages (with payment gateway)
				else if (data.data?.data?.authorization_url) {
					setPaymentUrl(data.data.data.authorization_url);
					window.location.href = data.data.data.authorization_url;
				} else {
					throw new Error('Invalid response from payment gateway.');
				}
			} else {
				throw new Error('Payment initialization failed.');
			}
		} catch (error) {
			showNotification('error', error.message || 'An error occurred.');
		} finally {
			setIsSubmitting(false);
		}
	};

	const tokenPackages = [
		{
			id: 1,
			name: 'Basic',
			tokens: 100,
			amount: 0,
			features: ['Basic Support', 'Standard Processing'],
			isPopular: false,
		},
		{
			id: 2,
			name: 'Standard',
			tokens: 250,
			amount: 2000,
			features: ['Enhanced Support', 'Faster Processing'],
			isPopular: false,
		},
		{
			id: 3,
			name: 'Professional',
			tokens: 700,
			amount: 5000,
			features: ['Priority Support', 'Fast Processing'],
			isPopular: true,
		},
		{
			id: 4,
			name: 'Enterprise',
			tokens: 1500,
			amount: 10000,
			features: ['24/7 Support', 'Instant Processing', 'Premium Features'],
			isPopular: false,
		},
	];

	useEffect(() => {
		const queryParams = new URLSearchParams(window.location.search);
		const status = queryParams.get('status');
		const message = queryParams.get('message');
		const tokens = queryParams.get('tokens');

		const notifications = {
			success: tokens
				? `Payment verified! ${tokens} tokens added to your account.`
				: 'Payment verified and you are now an admin!',
			already_admin: 'You are already an admin.',
			failed: message || 'Payment failed.',
			error: message || 'An error occurred during verification.',
		};

		if (status && notifications[status]) {
			showNotification(
				status === 'already_admin' ? 'info' : status,
				notifications[status],
			);
		}

		window.history.replaceState({}, document.title, window.location.pathname);
	}, []);

	// Determine button text for each package
	const getButtonText = (pkg) => {
		return pkg.amount === 0 ? 'Activate Free Package' : 'Proceed to Payment';
	};

	return (
		<div className='min-h-screen bg-background'>
			<Navbar />
			<div className='max-w-6xl mx-auto px-4 py-12'>
				<div className='text-center mb-12'>
					<h1 className='text-4xl font-bold text-text mb-4'>
						Enterprise Admin Access
					</h1>
					<p className='text-lg text-gray-600 max-w-2xl mx-auto'>
						Unlock advanced features and admin privileges with our token
						packages. Higher packages offer greater value!
					</p>
					<p className='text-md text-gray-500 mt-2'>
						Choose your plan and elevate your experience.
					</p>
				</div>

				{notification.message && (
					<Alert
						className={`mb-8 mx-auto max-w-2xl ${
							notification.type === 'success'
								? 'bg-green-50 border-green-200 text-green-800'
								: notification.type === 'info'
								? 'bg-blue-50 border-blue-200 text-blue-800'
								: 'bg-red-50 border-red-200 text-red-800'
						}`}>
						{notification.type === 'success' ? (
							<FaCheckCircle className='h-5 w-5' />
						) : (
							<FaExclamationCircle className='h-5 w-5' />
						)}
						<AlertDescription>{notification.message}</AlertDescription>
					</Alert>
				)}
				{user ? (
					<div className='grid md:grid-cols-4 gap-6 mb-12'>
						{tokenPackages.map((pkg) => (
							<Dialog key={pkg.id}>
								<DialogTrigger asChild>
									<Card
										className={`cursor-pointer transition-all hover:shadow-lg hover:scale-105 ${
											selectedPackage?.id === pkg.id
												? 'ring-2 ring-blue-500 shadow-lg'
												: ''
										}`}
										onClick={() => setSelectedPackage(pkg)}>
										<CardHeader>
											<div className='flex justify-between items-center'>
												<CardTitle className='flex items-center gap-2'>
													<FaCrown
														className={`h-5 w-5 ${
															pkg.id === 4
																? 'text-yellow-500'
																: pkg.id === 3
																? 'text-gray-400'
																: pkg.id === 2
																? 'text-gray-500'
																: 'text-gray-600'
														}`}
													/>
													{pkg.name} - {pkg.tokens} Tokens
												</CardTitle>
												{pkg.isPopular && (
													<span className='bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded'>
														Most Popular
													</span>
												)}
											</div>
											<CardDescription>
												<span className='text-2xl font-bold text-text'>
													{pkg.amount === 0
														? 'Free'
														: `₦${pkg.amount.toLocaleString()}`}
												</span>
											</CardDescription>
										</CardHeader>
										<CardContent>
											<Separator className='mb-4' />
											<ul className='space-y-2'>
												{pkg.features.map((feature, idx) => (
													<li
														key={idx}
														className='flex items-center gap-2 text-sm text-gray-600'>
														<FaShieldAlt className='h-4 w-4 text-blue-500' />
														{feature}
													</li>
												))}
											</ul>
										</CardContent>
									</Card>
								</DialogTrigger>
								<DialogContent>
									<DialogHeader>
										<DialogTitle>Confirm {pkg.name} Package</DialogTitle>
										<DialogDescription>
											{pkg.amount === 0
												? `You are about to activate the free ${pkg.name} package which includes ${pkg.tokens} tokens and admin access. Do you want to proceed?`
												: `You are about to purchase the ${
														pkg.name
												  } package which includes ${
														pkg.tokens
												  } tokens and admin access for ₦${pkg.amount.toLocaleString()}. Do you want to proceed?`}
										</DialogDescription>
									</DialogHeader>
									<DialogFooter>
										<DialogClose asChild>
											<Button variant='outline'>Cancel</Button>
										</DialogClose>
										<Button
											onClick={handlePayment}
											disabled={isSubmitting}
											className='bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2'>
											{isSubmitting ? (
												<>
													<FaSpinner className='animate-spin h-5 w-5' />
													Processing...
												</>
											) : (
												getButtonText(selectedPackage || pkg)
											)}
										</Button>
									</DialogFooter>
								</DialogContent>
							</Dialog>
						))}
					</div>
				) : (
					<div className='text-center mb-12 flex-col'>
						<p className='text-center mt-4 text-4xl text-gray-600'>
							Log in to access enterprise features.
						</p>
						<Link
							href='/auth/login'
							className='text-lg text-blue-600 hover:text-blue-800'>
							Login
						</Link>
					</div>
				)}

				{paymentUrl && (
					<p className='text-center mt-4 text-gray-600'>
						Redirecting to secure payment gateway...
					</p>
				)}
			</div>
		</div>
	);
};

// Wrap the component in Suspense for URLSearchParams
const PaymentAndPromotionPage = () => {
	return (
		<Suspense
			fallback={
				<div className='min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50'>
					<FaSpinner className='animate-spin h-10 w-10 text-blue-500' />
				</div>
			}>
			<PaymentAndPromotion />
		</Suspense>
	);
};

export default PaymentAndPromotionPage;
