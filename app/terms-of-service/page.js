'use client';
import Navbar from '@/components/Navbar';
import React from 'react';

const TermsOfService = () => {
	return (
		<div className='min-h-screen bg-gradient-to-br from-purple-900 via-blue-800 to-purple-900 flex flex-col'>
			<Navbar />
			<div className='flex-1 flex justify-center p-6'>
				<div className='bg-white/10 backdrop-blur-md rounded-3xl shadow-2xl max-w-4xl w-full p-10 text-white space-y-8'>
					<header className='text-center'>
						<h1 className='text-4xl font-extrabold mb-2'>Terms of Service</h1>
						<p className='text-gray-300 text-lg'>
							Please read these terms carefully before using UploadDoc.
						</p>
					</header>

					<section>
						<h2 className='text-2xl font-bold mb-2 text-purple-200'>
							1. Acceptance of Terms
						</h2>
						<p className='text-gray-200 text-lg'>
							By accessing and using our services, you accept and agree to be
							bound by these terms. If you do not agree, please do not use this
							service.
						</p>
					</section>

					<section>
						<h2 className='text-2xl font-bold mb-2 text-purple-200'>
							2. Changes to Terms
						</h2>
						<p className='text-gray-200 text-lg'>
							We reserve the right to modify these terms at any time. Continued
							use of the service after changes will be considered acceptance of
							the new terms.
						</p>
					</section>

					<section>
						<h2 className='text-2xl font-bold mb-2 text-purple-200'>
							3. User Responsibilities
						</h2>
						<p className='text-gray-200 text-lg'>
							You are responsible for your actions and content shared on
							UploadDoc. Only post content you are comfortable sharing publicly.
						</p>
					</section>

					<section>
						<h2 className='text-2xl font-bold mb-2 text-purple-200'>
							4. Privacy
						</h2>
						<p className='text-gray-200 text-lg'>
							Our Privacy Policy details how we handle your personal data. By
							using our services, you agree to our data practices as described.
						</p>
					</section>

					<section>
						<h2 className='text-2xl font-bold mb-2 text-purple-200'>
							5. Termination
						</h2>
						<p className='text-gray-200 text-lg'>
							We may suspend or terminate access to the service immediately if
							you breach these terms or for any other reason at our sole
							discretion.
						</p>
					</section>

					<section>
						<h2 className='text-2xl font-bold mb-2 text-purple-200'>
							6. Governing Law
						</h2>
						<p className='text-gray-200 text-lg'>
							These terms are governed by the laws of the jurisdiction where
							UploadDoc operates, without regard to its conflict of law
							principles.
						</p>
					</section>

					<section>
						<h2 className='text-2xl font-bold mb-2 text-purple-200'>
							7. Contact Us
						</h2>
						<p className='text-gray-200 text-lg'>
							For any questions regarding these Terms, please reach out at{' '}
							<a
								href='mailto:<EMAIL>'
								className='text-blue-300 hover:underline'>
								<EMAIL>
							</a>
							.
						</p>
					</section>
				</div>
			</div>
		</div>
	);
};

export default TermsOfService;
