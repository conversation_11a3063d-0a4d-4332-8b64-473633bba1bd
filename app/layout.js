import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from 'next/font/google';
import './globals.css';
import { AuthProvider } from './context/AuthContext';
import { DataProvider } from './context/DataContext';
import { ThemeProvider } from './context/ThemeContext';
import { AnalyticsProvider } from './context/AnalyticsContext';
import Footer from '@/components/Footer';
export const metadata = {
	title: 'UploadDoc',
	description:
		'UploadDoc is a powerful document management and printing platform for students, professionals, and vendors. Submit, track, manage, and print documents with ease. Find trusted printing providers around you and organize all your documents in one place.',
	keywords:
		'document management system, document management system, upload documents online, document printing, find printing services, cyber cafe near me, online printing platform, submit documents for printing, print documents online, student project upload, vendor document portal, UploadDoc',
	author: 'UploadDoc Team',
	robots: 'index, follow',
	openGraph: {
		title: 'UploadDoc | Manage, Submit & Print Documents Online',
		description:
			'Upload, organize, and manage documents online. Easily connect with print vendors around you for fast, secure document printing and project submissions.',
		url: 'https://uploaddoc.app',
		type: 'website',
		image: 'https://uploaddoc.app/uploaddoc.png',
	},
	twitter: {
		card: 'summary_large_image',
		title: 'UploadDoc | Manage, Submit & Print Documents Online',
		description:
			'UploadDoc helps you submit and print documents quickly and securely. Find nearby vendors and manage all your document needs in one place.',
		image: 'https://uploaddoc.app/uploaddoc.png',
	},
};

export default function RootLayout({ children }) {
	return (
		<html
			lang='en'
			dir='ltr'>
			<head>
				<title>{metadata.title}</title>
				<link
					rel='canonical'
					href='https://uploaddoc.app'
				/>

				<meta
					name='description'
					content={metadata.description}
				/>
				<meta
					name='keywords'
					content={metadata.keywords}
				/>
				<meta
					name='author'
					content={metadata.author}
				/>
				<meta
					name='robots'
					content={metadata.robots}
				/>

				{/* Open Graph Meta Tags */}
				<meta
					property='og:title'
					content={metadata.openGraph.title}
				/>
				<meta
					property='og:description'
					content={metadata.openGraph.description}
				/>
				<meta
					property='og:url'
					content={metadata.openGraph.url}
				/>
				<meta
					property='og:type'
					content={metadata.openGraph.type}
				/>
				<meta
					property='og:image'
					content={metadata.openGraph.image}
				/>

				{/* Twitter Meta Tags */}
				<meta
					name='twitter:card'
					content={metadata.twitter.card}
				/>
				<meta
					name='twitter:title'
					content={metadata.twitter.title}
				/>
				<meta
					name='twitter:description'
					content={metadata.twitter.description}
				/>
				<meta
					name='twitter:image'
					content={metadata.twitter.image}
				/>
			</head>
			<body>
				<AuthProvider>
					<DataProvider>
						<ThemeProvider>
							<AnalyticsProvider>
								{children}
								<Footer aria-hidden='true' />
							</AnalyticsProvider>
						</ThemeProvider>
					</DataProvider>
				</AuthProvider>
			</body>
		</html>
	);
}
