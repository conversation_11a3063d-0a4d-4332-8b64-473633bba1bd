'use client';
import { useRouter } from 'next/navigation';
import { useEffect, useContext } from 'react';
import { AuthContext } from '@/app/context/AuthContext';

const GoogleAuthCallback = () => {
	const router = useRouter();
	const { handleGoogleCallback } = useContext(AuthContext);

	useEffect(() => {
		const processCallback = async () => {
			try {
				await handleGoogleCallback();
				router.push('/submit'); // Adjust the redirect as needed
			} catch (error) {
				console.error('Google login error:', error);
				router.push('/');
			}
		};

		processCallback();
	}, [handleGoogleCallback, router]);

	return <p>Processing login...</p>;
};

export default GoogleAuthCallback;
