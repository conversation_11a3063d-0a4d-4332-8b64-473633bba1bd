'use client';

import Image from 'next/image';
import {
	Download,
	Shield,
	Upload,
	Search,
	Star,
	ChevronLeft,
	ChevronRight,
} from 'lucide-react';
import Navbar from '@/components/Navbar';

const DownloadPage = () => {
	const appScreenshots = [
		{
			url: 'https://ik.imagekit.io/uploaddoc/App%20Screenshot/splash.png?updatedAt=1749757003761',
			alt: 'UploadDoc Splash Screen',
		},
		{
			url: 'https://ik.imagekit.io/uploaddoc/App%20Screenshot/Screenshot_20250612-113402.png?updatedAt=1749757005387',
			alt: 'UploadDoc Home Screen',
		},
		{
			url: 'https://ik.imagekit.io/uploaddoc/App%20Screenshot/upload.png?updatedAt=1749756974824',
			alt: 'UploadDoc Upload Screen',
		},
		{
			url: 'https://ik.imagekit.io/uploaddoc/App%20Screenshot/Screenshot_20250612-113412.png?updatedAt=1749756994788',
			alt: 'UploadDoc Features Screen',
		},
		{
			url: 'https://ik.imagekit.io/uploaddoc/App%20Screenshot/Screenshot_20250612-113428.png?updatedAt=1749757010983',
			alt: 'UploadDoc Profile Screen',
		},
		{
			url: 'https://ik.imagekit.io/uploaddoc/App%20Screenshot/Screenshot_20250612-113436.png?updatedAt=1749757009790',
			alt: 'UploadDoc Settings Screen',
		},
		{
			url: 'https://ik.imagekit.io/uploaddoc/App%20Screenshot/Screenshot_20250612-113442.png?updatedAt=1749756999273',
			alt: 'UploadDoc Vendor Screen',
		},
		{
			url: 'https://ik.imagekit.io/uploaddoc/App%20Screenshot/Screenshot_20250612-113452.png?updatedAt=1749756965505',
			alt: 'UploadDoc Search Screen',
		},
	];

	const features = [
		{
			icon: <Upload className='w-6 h-6' />,
			title: 'Quick Document Submission',
			description:
				'Upload documents directly from your device to your chosen printing vendor.',
		},
		{
			icon: <Shield className='w-6 h-6' />,
			title: 'Secure Storage',
			description:
				'Your documents are stored securely and can only be accessed by authorized admins.',
		},
		{
			icon: <Search className='w-6 h-6' />,
			title: 'Find Vendors Easily',
			description:
				'Locate and compare nearby vendors by location, rating, pricing, and more.',
		},
		{
			icon: <Star className='w-6 h-6' />,
			title: 'Real-Time Tracking',
			description:
				'Keep track of document submissions with file names and sender details.',
		},
	];

	return (
		<>
			<Navbar />
			<div className='min-h-screen bg-background'>
				{/* Hero Section */}
				<section className='py-20 px-6'>
					<div className='max-w-7xl mx-auto'>
						<div className='text-center mb-12'>
							<h1 className='text-4xl md:text-5xl font-bold mb-6 text-text'>
								Download UploadDoc for Android
							</h1>
							<p className='text-xl text-text/80 mb-8 max-w-2xl mx-auto'>
								Your all-in-one solution for seamless document submission and
								printing, now in your pocket.
							</p>
							<div className='flex justify-center gap-6'>
								<a
									href='https://drive.google.com/file/d/14Qoadny-TLthO4ju23ovBDeHjGfIo_Gl/view?usp=sharing'
									className='btn-3d bg-gradient-to-r from-primary to-accent text-white px-8 py-4 rounded-lg flex items-center gap-2 hover:opacity-90 transition-all duration-300'>
									<Download className='w-5 h-5' />
									Download v1.0.25
								</a>
							</div>
							<div className='mt-4 text-text/60 text-sm'>
								Latest version: v1.0.25 (d709d6d)
							</div>
						</div>
					</div>
				</section>

				{/* App Screenshots */}
				<section className='py-20 px-6 bg-gradient-to-b from-background to-primary/10'>
					<div className='max-w-7xl mx-auto'>
						<h2 className='text-3xl font-bold text-center mb-12 text-text'>
							App Preview
						</h2>
						<div className='relative'>
							<div className='flex overflow-x-auto gap-6 pb-8 px-4 snap-x snap-mandatory hide-scrollbar'>
								{appScreenshots.map((screenshot, index) => (
									<div
										key={index}
										className='shrink-0 snap-center'
										style={{ width: '280px' }}>
										<div className='relative w-[280px] h-[642px] rounded-3xl overflow-hidden border-8 border-gray-800 shadow-xl transform transition-transform hover:scale-105'>
											<Image
												src={screenshot.url}
												alt={screenshot.alt}
												width={720}
												height={1650}
												className='object-cover'
											/>
											<div className='absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4'></div>
										</div>
									</div>
								))}
							</div>

							{/* Scroll indicators */}
							<div className='absolute -bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2'>
								{appScreenshots.map((_, index) => (
									<button
										key={index}
										className='w-2 h-2 rounded-full bg-primary/20 transition-colors hover:bg-primary'
										onClick={() => {
											const container = document.querySelector('.snap-x');
											const items = document.querySelectorAll('.snap-center');
											container?.scrollTo({
												left: items[index].offsetLeft - container.offsetLeft,
												behavior: 'smooth',
											});
										}}
										aria-label={`Go to screenshot ${index + 1}`}
									/>
								))}
							</div>

							{/* Scroll arrows */}
							<button
								className='absolute top-1/2 left-4 -translate-y-1/2 bg-background/80 backdrop-blur-sm p-2 rounded-full shadow-lg text-text hover:bg-background transition-colors'
								onClick={() => {
									const container = document.querySelector('.snap-x');
									container?.scrollBy({ left: -300, behavior: 'smooth' });
								}}
								aria-label='Previous screenshot'>
								<ChevronLeft className='h-6 w-6' />
							</button>
							<button
								className='absolute top-1/2 right-4 -translate-y-1/2 bg-background/80 backdrop-blur-sm p-2 rounded-full shadow-lg text-text hover:bg-background transition-colors'
								onClick={() => {
									const container = document.querySelector('.snap-x');
									container?.scrollBy({ left: 300, behavior: 'smooth' });
								}}
								aria-label='Next screenshot'>
								<ChevronRight className='h-6 w-6' />
							</button>
						</div>
					</div>
				</section>

				{/* Features */}
				<section className='py-20 px-6'>
					<div className='max-w-7xl mx-auto'>
						<h2 className='text-3xl font-bold text-center mb-12 text-text'>
							Key Features
						</h2>
						<div className='grid grid-cols-1 md:grid-cols-2 gap-8'>
							{features.map((feature, index) => (
								<div
									key={index}
									className='card-3d p-6 rounded-xl bg-primary/5'>
									<div className='flex items-start gap-4'>
										<div className='shrink-0 w-12 h-12 rounded-full bg-gradient-to-r from-primary to-accent flex items-center justify-center text-white'>
											{feature.icon}
										</div>
										<div>
											<h3 className='text-xl font-semibold mb-2 text-text'>
												{feature.title}
											</h3>
											<p className='text-text/80'>{feature.description}</p>
										</div>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Known Issues */}
				<section className='py-20 px-6 bg-gradient-to-b from-primary/10 to-background'>
					<div className='max-w-7xl mx-auto'>
						<h2 className='text-3xl font-bold text-center mb-12 text-text'>
							Known Issues
						</h2>
						<div className='max-w-3xl mx-auto space-y-6'>
							<div className='card-3d p-6 rounded-xl bg-yellow-500/10 border border-yellow-500/20'>
								<h3 className='text-xl font-semibold mb-4 text-text'>
									Google Sign-In
								</h3>
								<p className='text-text/80 mb-4'>
									There's a major issue with Google Sign-In on the Android app.
								</p>
								<p className='text-text/80'>
									<strong>Workaround:</strong> Use the web version to reset your
									password via the "Forgot Password" link, then log in with your
									email and the new password in the app.
								</p>
							</div>

							{/* <div className='card-3d p-6 rounded-xl bg-yellow-500/10 border border-yellow-500/20'>
								<h3 className='text-xl font-semibold mb-4 text-text'>
									File Download for Vendors
								</h3>
								<p className='text-text/80'>
									Vendors may encounter issues downloading files directly to
									their device. However, accepting and deleting documents works
									perfectly.
								</p>
							</div> */}
						</div>
					</div>
				</section>
			</div>
		</>
	);
};

export default DownloadPage;
