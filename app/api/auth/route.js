import { NextResponse } from 'next/server';

const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL; // Make sure this is defined!

export async function POST(req) {
	try {
		const { email, password, name } = await req.json();

		const res = await fetch(`${BACKEND_URL}/api/auth/register`, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({ email, password, name }),
		});

		// Ensure response is JSON
		const data = await res.json();

		if (!res.ok) {
			return NextResponse.json({ error: data.message }, { status: res.status });
		}

		return NextResponse.json(data, { status: 200 });
	} catch (error) {
		console.error('Server Error:', error);
		return NextResponse.json({ error: 'Server error' }, { status: 500 });
	}
}
