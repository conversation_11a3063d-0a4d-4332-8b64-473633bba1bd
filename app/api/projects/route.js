import { NextResponse } from 'next/server';

export async function POST(req) {
	try {
		const formData = await req.formData();
		const res = await fetch(`${process.env.BACKEND_URL}/api/projects/upload`, {
			method: 'POST',
			body: formData,
		});

		const data = await res.json();
		return NextResponse.json(data, { status: res.status });
	} catch (error) {
		return NextResponse.json({ error: 'Upload failed' }, { status: 500 });
	}
}

export async function DELETE(req) {
	try {
		const { id } = await req.json();
		const res = await fetch(`${process.env.BACKEND_URL}/api/projects/${id}`, {
			method: 'DELETE',
		});

		const data = await res.json();
		return NextResponse.json(data, { status: res.status });
	} catch (error) {
		return NextResponse.json({ error: 'Delete failed' }, { status: 500 });
	}
}
