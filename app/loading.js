'use client';

import { FaSpinner } from 'react-icons/fa';

export default function Loading() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="relative">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-10 left-10 w-40 h-40 rounded-full bg-primary blur-3xl"></div>
          <div className="absolute bottom-10 right-10 w-60 h-60 rounded-full bg-secondary blur-3xl"></div>
        </div>
        
        <div className="relative z-10 flex flex-col items-center">
          <div className="animate-spin text-primary mb-4">
            <FaSpinner className="w-12 h-12" />
          </div>
          <p className="text-text/80 text-lg font-medium">Loading...</p>
        </div>
      </div>
    </div>
  );
}
