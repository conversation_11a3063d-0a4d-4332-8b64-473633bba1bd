'use client';
import Link from 'next/link';
import { Separator } from '@/components/ui/separator';
import { MailIcon } from 'lucide-react';
import { FaGithub, FaGlobe } from 'react-icons/fa';
import { useContext } from 'react';
import { AuthContext } from '@/app/context/AuthContext';
import { useTheme } from '@/app/context/ThemeContext';

const Footer = () => {
	const year = new Date().getFullYear();
	const { user } = useContext(AuthContext);
	const { theme } = useTheme();

	return (
		<footer className='bg-primary/10 text-text mt-auto border-t border-primary/10'>
			<div className='container mx-auto px-6 py-12'>
				{/* Upper Section with Logo and Navigation */}
				<div className='grid grid-cols-1 md:grid-cols-3 gap-8 mb-8'>
					{/* Brand Section */}
					<div className='space-y-4'>
						<h3 className='font-bold text-xl'>UploadDoc</h3>
						<p className='text-text/70 text-sm max-w-xs'>
							Simple, secure document uploading and management for
							professionals.
						</p>
					</div>

					{/* Quick Links */}
					<div className='space-y-4'>
						<h4 className='font-semibold text-lg'>Quick Links</h4>
						<nav className='flex flex-col space-y-2'>
							{user?.isAdmin && (
								<Link
									href='/dashboard'
									className='text-text/70 hover:text-primary transition-colors'>
									Dashboard
								</Link>
							)}

							<Link
								href='/submit'
								className='text-text/70 hover:text-primary transition-colors'>
								Documents
							</Link>
							<Link
								href='/about'
								className='text-text/70 hover:text-primary transition-colors'>
								About Us
							</Link>
							<Link
								href='#'
								className='text-text/70 hover:text-primary transition-colors'>
								FAQ
							</Link>
						</nav>
					</div>

					{/* Contact */}
					<div className='space-y-4'>
						<h4 className='font-semibold text-lg'>Contact Us</h4>
						<div className='space-y-2'>
							<a
								href='mailto:<EMAIL>'
								className='flex items-center text-text/70 hover:text-primary transition-colors'>
								<MailIcon className='h-4 w-4 mr-2' />
								<EMAIL>
							</a>
							<div className='flex space-x-4 mt-4'>
								<a
									href='https://github.com/DanielAgbeni'
									className='text-text/70 hover:text-primary transition-colors flex item-center'>
									<FaGithub className='h-5 w-5 mr-2' /> Daniel Agbeni
								</a>
							</div>
							<div className='flex space-x-4 mt-4'>
								<a
									href='https://danielagbeni.netlify.app'
									className='text-text/70 hover:text-primary transition-colors flex item-center'>
									<FaGlobe className='h-5 w-5 mr-2' /> Daniel Agbeni
								</a>
							</div>
						</div>
					</div>
				</div>

				{/* Divider */}
				<Separator className='bg-primary/30 my-6' />

				{/* Bottom Section */}
				<div className='flex flex-col md:flex-row justify-between items-center pt-2'>
					<p className='text-sm text-text/70'>
						© {year} UploadDoc. All rights reserved.
					</p>

					<div className='flex space-x-6 mt-4 md:mt-0'>
						<Link
							href='/terms-of-service'
							className='text-sm text-text/70 hover:text-primary transition-colors'>
							Terms of Service
						</Link>
						<Link
							href='/privacy-policy'
							className='text-sm text-text/70 hover:text-primary transition-colors'>
							Privacy Policy
						</Link>
					</div>
				</div>
			</div>
		</footer>
	);
};

export default Footer;
