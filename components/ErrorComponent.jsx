import React from 'react';

const ErrorComponent = ({ error, handleRetry }) => {
	return (
		<div className='bg-white/10 backdrop-blur-md rounded-xl p-8 text-center'>
			<div className='text-white text-xl mb-4'>{error}</div>
			<button
				onClick={handleRetry}
				className='bg-white/20 hover:bg-white/30 text-white py-2 px-6 rounded-lg transition-colors'>
				Try Again
			</button>
		</div>
	);
};

export default ErrorComponent;
