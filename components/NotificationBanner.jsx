import React from 'react';

// Notification Permission Banner
const NotificationBanner = ({
	notificationStatus,
	requestNotificationPermission,
}) => {
	if (notificationStatus === 'granted') return null;

	return (
		<div className='bg-blue-50 border-l-4 border-blue-500 p-4 mb-4 flex items-center justify-between'>
			<div className='flex items-center'>
				<svg
					className='w-6 h-6 text-blue-500 mr-3'
					fill='none'
					stroke='currentColor'
					viewBox='0 0 24 24'
					xmlns='http://www.w3.org/2000/svg'>
					<path
						strokeLinecap='round'
						strokeLinejoin='round'
						strokeWidth={2}
						d='M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9'
					/>
				</svg>
				<div>
					<p className='font-bold text-blue-800'>Enable Notifications</p>
					<p className='text-blue-600 text-sm'>
						Get updated instantly when a document is sent to you
					</p>
				</div>
			</div>
			<button
				onClick={requestNotificationPermission}
				className='bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition duration-300'>
				Enable Notifications
			</button>
		</div>
	);
};

export default NotificationBanner;
