'use client';
import React from 'react';

const NoResultsComponent = ({ searchQuery, setSearchQuery }) => {
	return (
		<div className='bg-primary/5 backdrop-blur-md rounded-xl p-12 text-center border border-secondary/20'>
			<div className='text-text text-xl mb-2'>No Vendors found</div>
			<p className='text-text/70 mb-6'>
				{searchQuery
					? `No results for "${searchQuery}"`
					: 'No vendors are available at the moment.'}
			</p>
			{searchQuery && (
				<button
					onClick={() => setSearchQuery('')}
					className='bg-primary/10 hover:bg-primary/20 text-text py-2 px-6 rounded-lg transition-colors'>
					Clear Search
				</button>
			)}
		</div>
	);
};

export default NoResultsComponent;
