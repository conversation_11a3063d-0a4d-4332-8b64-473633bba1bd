'use client';
import React, { useState } from 'react';
import { Search, Filter } from 'lucide-react';

const SearchAndFilter = ({
	searchQuery,
	setSearchQuery,
	filters,
	setFilters,
	showFilters,
	setShowFilters,
	resetFilters,
}) => {
	return (
		<div className='bg-primary/5 backdrop-blur-md rounded-xl p-6 mb-8 card'>
			<div className='relative mb-4'>
				<div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
					<Search className='h-5 w-5 text-text/60' />
				</div>
				<input
					type='text'
					placeholder='Search by name, location, or contact...'
					className='w-full bg-background text-text py-3 pl-10 pr-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent border border-secondary'
					value={searchQuery}
					onChange={(e) => setSearchQuery(e.target.value)}
				/>
			</div>

			{/* <div className='flex justify-between items-center'>
				<button
					onClick={() => setShowFilters(!showFilters)}
					className='flex items-center text-text bg-primary/10 px-4 py-2 rounded-lg hover:bg-primary/20 transition'>
					<Filter className='w-4 h-4 mr-2' />
					Filters
				</button>

				{(searchQuery || filters.location || filters.priceRange) && (
					<button
						onClick={resetFilters}
						className='text-text bg-primary/10 px-4 py-2 rounded-lg hover:bg-primary transition'>
						Clear All
					</button>
				)}
			</div> */}

			{showFilters && (
				<div className='mt-4 grid grid-cols-1 md:grid-cols-2 gap-4'>
					<div>
						<label className='block text-text/90 mb-1'>Location</label>
						<input
							type='text'
							placeholder='Filter by location'
							className='w-full bg-background text-text py-2 px-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent border border-secondary'
							value={filters.location}
							onChange={(e) =>
								setFilters({ ...filters, location: e.target.value })
							}
						/>
					</div>
					<div>
						<label className='block text-text/90 mb-1'>Price Range (₦)</label>
						<select
							className='w-full bg-background text-text py-2 px-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent border border-secondary'
							value={filters.priceRange}
							onChange={(e) =>
								setFilters({ ...filters, priceRange: e.target.value })
							}>
							<option value=''>Any price</option>
							<option value='0/50'>0-50</option>
							<option value='50/100'>50-100</option>
							<option value='100/150'>100-150</option>
							<option value='150/200'>150-200</option>
							<option value='200'>200+</option>
						</select>
					</div>
				</div>
			)}
		</div>
	);
};

export default SearchAndFilter;
