'use client';
import React from 'react';
import {
	MapPin,
	Clock,
	CheckCircle,
	Tag,
	Phone,
	MessageSquare,
} from 'lucide-react';
import { TbCurrencyNaira } from 'react-icons/tb';
import Rating from '@/components/Rating';

const ProviderCard = ({ admin, handleAdminSelect, handleRatingUpdated }) => {
	const formatDiscountRates = (discounts) => {
		if (!discounts || discounts.length === 0) {
			return 'No discounts available';
		}

		return discounts
			.map(
				(discount) =>
					`Pages ${discount.minPages}-${discount.maxPages}: ${discount.discount}% off`,
			)
			.join(', ');
	};
	return (
		<div className='bg-primary/5 backdrop-blur-md rounded-xl overflow-hidden shadow-lg transition-all duration-200 hover:shadow-xl card-3d'>
			<div className='p-6'>
				<div className='flex justify-between items-start mb-4'>
					<h2 className='text-2xl font-bold text-text'>
						{admin.name || admin.username}
					</h2>
					<Rating
						adminId={admin._id}
						initialRating={admin.rating}
						onRatingUpdated={(newRating) =>
							handleRatingUpdated(admin._id, newRating)
						}
					/>
				</div>
				<div className='mb-4'>
					<p className='text-text/90 text-sm'>
						{admin.additionalInfo || 'No description available'}
					</p>
				</div>

				<div className='space-y-2 mb-4'>
					<div className='flex items-center'>
						<MapPin className='w-4 h-4 text-secondary mr-2' />
						<span className='text-text/90 text-sm'>
							{admin.printingLocation || 'Not specified'}
						</span>
					</div>

					<div className='flex items-center'>
						<Clock className='w-4 h-4 text-secondary mr-2' />
						<span className='text-text/90 text-sm'>
							{admin.openingHours || 'Not specified'}
						</span>
					</div>

					<div className='flex items-center'>
						<TbCurrencyNaira className='w-4 h-4 text-secondary mr-2' />
						<span className='text-text/90 text-sm'>
							{admin.printingCost
								? `₦${admin.printingCost} per page`
								: 'Price not available'}
						</span>
					</div>

					{admin.supportContact && (
						<div className='flex items-center'>
							<Phone className='w-4 h-4 text-secondary mr-2' />
							<span className='text-text/90 text-sm'>
								{admin.supportContact}
							</span>
						</div>
					)}

					{admin.isVerified && (
						<div className='flex items-center'>
							<CheckCircle className='w-4 h-4 text-accent mr-2' />
							<span className='text-accent text-sm'>Verified</span>
						</div>
					)}

					{admin.discountRates && admin.discountRates.length > 0 && (
						<div className='flex items-center'>
							<Tag className='w-4 h-4 text-orange-400 mr-2' />
							<span className='text-orange-400 text-sm'>
								{formatDiscountRates(admin.discountRates)}
							</span>
						</div>
					)}

					<div className='flex items-center'>
						<MessageSquare className='w-4 h-4 text-text/60 mr-2' />
						<span className='text-text/60 text-sm'>
							{admin.reviews ? admin.reviews.length : 0} Reviews
						</span>
					</div>
				</div>
				<button
					onClick={() => handleAdminSelect(admin)}
					className='w-full bg-gradient-to-r from-primary to-accent text-white py-2 px-4 rounded-md hover:opacity-90 transition'>
					Select Vendor
				</button>
			</div>
		</div>
	);
};

export default ProviderCard;
