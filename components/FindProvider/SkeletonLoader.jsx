'use client';
import React from 'react';

const SkeletonLoader = () => {
	return (
		<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
			{[...Array(6)].map((_, index) => (
				<div
					key={index}
					className='bg-primary/5 backdrop-blur-md rounded-xl overflow-hidden shadow-lg animate-pulse border border-secondary/20'>
					<div className='p-6'>
						<div className='h-6 bg-primary/10 rounded w-3/4 mb-4'></div>
						<div className='space-y-4'>
							<div className='h-4 bg-primary/10 rounded w-1/2'></div>
							<div className='h-4 bg-primary/10 rounded w-3/4'></div>
							<div className='h-4 bg-primary/10 rounded w-1/2'></div>
						</div>
					</div>
				</div>
			))}
		</div>
	);
};

export default SkeletonLoader;
