'use client';
import React, { useState } from 'react';
import {
	Fa<PERSON><PERSON>ner,
	FaExclamationCircle,
	FaTimes,
	FaTrash,
	FaFileAlt,
	FaUserTie,
	FaClock,
	FaCheck,
	FaHourglassHalf,
} from 'react-icons/fa';
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import Pagination from '@/components/Pagination';

const SubmittedDocuments = ({
	userProjects,
	admins,
	onDelete,
	isLoading,
	error,
	pagination,
	onPageChange,
	onLimitChange,
	newlySubmittedProject,
}) => {
	const [projectToDelete, setProjectToDelete] = useState(null);

	const getAdminName = (adminId) => {
		if (!adminId) return 'Unassigned';
		const admin = admins?.find((admin) => admin._id === adminId);
		return admin ? admin.name : 'Unknown Admin';
	};

	const getStatusColor = (status) => {
		switch (status) {
			case 'accepted':
				return 'text-green-400';
			case 'rejected':
				return 'text-red-400';
			default:
				return 'text-yellow-400';
		}
	};

	const getStatusIcon = (status) => {
		switch (status) {
			case 'accepted':
				return <FaCheck className='h-4 w-4' />;
			case 'rejected':
				return <FaTimes className='h-4 w-4' />;
			default:
				return <FaHourglassHalf className='h-4 w-4' />;
		}
	};

	const formatDate = (dateString) => {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	};

	const isNewlySubmitted = (project) => {
		return (
			newlySubmittedProject &&
			(newlySubmittedProject._id === project._id ||
				newlySubmittedProject.title === project.title)
		);
	};

	if (isLoading) {
		return (
			<div className='flex justify-center p-8'>
				<FaSpinner className='animate-spin h-8 w-8 text-purple-500' />
			</div>
		);
	}

	if (error) {
		return (
			<div className='p-4 bg-red-500 bg-opacity-20 border border-red-500 rounded-lg'>
				<div className='flex items-center gap-2'>
					<FaExclamationCircle className='h-5 w-5' />
					<p>{error}</p>
				</div>
			</div>
		);
	}

	// Handle the new response format
	const projects = Array.isArray(userProjects)
		? userProjects
		: userProjects?.projects || [];

	return (
		<div className='space-y-4'>
			{projects.length === 0 ? (
				<div className='text-center p-8 card rounded-lg'>
					<FaFileAlt className='mx-auto h-12 w-12 text-gray-400 mb-4' />
					<p className='text-gray-300'>No documents submitted yet.</p>
				</div>
			) : (
				projects.map((project) => (
					<div
						key={project._id}
						className={`card rounded-lg p-4 transition-all duration-500 ${
							isNewlySubmitted(project)
								? 'bg-gradient-to-r from-primary/20 to-accent/20 border-2 border-primary/50 shadow-lg animate-pulse'
								: 'bg-opacity-5 hover:bg-opacity-10'
						}`}>
						<div className='flex justify-between items-start'>
							<div className='space-y-2'>
								<div className='flex items-center gap-3'>
									<FaFileAlt className='h-5 w-5 text-purple-400' />
									<h3 className='font-semibold text-lg text-text'>
										{project.title}
									</h3>
									{isNewlySubmitted(project) && (
										<span className='px-2 py-1 bg-gradient-to-r from-green-500 to-green-600 text-white text-xs rounded-full font-medium animate-bounce'>
											Just Submitted!
										</span>
									)}
								</div>

								<div className='flex items-center gap-2 text-sm text-text'>
									<FaUserTie className='h-4 w-4' />
									<span>
										Assigned to: {getAdminName(project.assignedAdmin)}
									</span>
								</div>

								<div className='flex items-center gap-2 text-sm text-text'>
									<FaClock className='h-4 w-4' />
									<span>Submitted on: {formatDate(project.createdAt)}</span>
								</div>

								<div className='flex items-center gap-2'>
									<span
										className={`flex items-center gap-1 ${getStatusColor(
											project.status,
										)}`}>
										{getStatusIcon(project.status)}
										<span className='capitalize'>{project.status}</span>
									</span>
								</div>
							</div>

							<div className='flex gap-2'>
								<a
									href={project.fileUrl}
									target='_blank'
									rel='noopener noreferrer'
									className='px-3 py-1 bg-purple-500 hover:bg-purple-600 rounded text-sm flex items-center gap-1'>
									<FaFileAlt className='h-4 w-4' />
									View
								</a>
								<button
									onClick={() => setProjectToDelete(project)}
									className='px-3 py-1 bg-red-500 bg-opacity-20 hover:bg-opacity-30 rounded text-sm flex items-center gap-1 text-red-400'>
									<FaTrash className='h-4 w-4' />
									Delete
								</button>
							</div>
						</div>
					</div>
				))
			)}

			{/* Pagination component */}
			<Pagination
				pagination={pagination}
				onPageChange={onPageChange}
				onLimitChange={onLimitChange}
				itemName='documents'
				currentItems={projects}
			/>

			<AlertDialog
				open={!!projectToDelete}
				onOpenChange={() => setProjectToDelete(null)}>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>
							Are you sure you want to delete this document?
						</AlertDialogTitle>
						<AlertDialogDescription>
							This action cannot be undone. This will permanently delete your
							document "{projectToDelete?.title}".
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							className='bg-red-500 hover:bg-red-600'
							onClick={() => {
								onDelete(projectToDelete._id);
								setProjectToDelete(null);
							}}>
							Delete
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</div>
	);
};

export default SubmittedDocuments;
