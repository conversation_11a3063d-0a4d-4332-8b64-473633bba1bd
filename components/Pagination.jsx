'use client';
import React from 'react';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';

const Pagination = ({
	pagination,
	onPageChange,
	onLimitChange,
	itemName = 'items',
	currentItems = [],
}) => {
	if (!pagination) {
		return null;
	}

	const totalPages = pagination.totalPages || 1;
	const currentPage = pagination.currentPage || 1;
	const totalCount = pagination.totalCount || 0;
	const limit = pagination.limit || 10;

	if (totalPages <= 1 && totalCount <= 5) {
		return null;
	}

	return (
		totalPages > 1 && (
			<div className='flex justify-between items-center mt-6 px-4 bg-primary/5 rounded-lg py-3 border border-secondary/20'>
				<div className='text-sm text-text/80'>
					Showing {currentItems.length} of {totalCount} {itemName}
				</div>
				<div className='flex items-center space-x-2'>
					<button
						onClick={() => onPageChange(currentPage - 1)}
						disabled={currentPage === 1}
						className='px-3 py-1 rounded bg-primary/10 text-text hover:bg-primary/20 disabled:opacity-50 disabled:cursor-not-allowed'>
						<FaChevronLeft />
					</button>

					<span className='text-sm text-text'>
						Page {currentPage} of {totalPages}
					</span>

					<button
						onClick={() => onPageChange(currentPage + 1)}
						disabled={currentPage === totalPages}
						className='px-3 py-1 rounded bg-primary/10 text-text hover:bg-primary/20 disabled:opacity-50 disabled:cursor-not-allowed'>
						<FaChevronRight />
					</button>

					<select
						value={limit}
						onChange={(e) => onLimitChange(Number(e.target.value))}
						className='ml-4 border border-secondary/20 rounded px-2 py-1 text-sm bg-background text-text'>
						<option value='5'>5 per page</option>
						<option value='10'>10 per page</option>
						<option value='20'>20 per page</option>
						<option value='50'>50 per page</option>
					</select>
				</div>
			</div>
		)
	);
};

export default Pagination;
