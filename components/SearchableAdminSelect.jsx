'use client';
import { useState, useEffect, useRef } from 'react';
import { FaSearch, FaTimes } from 'react-icons/fa';

const SearchableAdminSelect = ({
	admins,
	selectedAdmin,
	onSelect,
	preSelectedAdminId,
}) => {
	const [isOpen, setIsOpen] = useState(false);
	const [searchTerm, setSearchTerm] = useState('');
	const [filteredAdmins, setFilteredAdmins] = useState(admins);
	const dropdownRef = useRef(null);
	const [internalSelectedAdmin, setInternalSelectedAdmin] =
		useState(selectedAdmin);

	useEffect(() => {
		const filtered = admins.filter(
			(admin) =>
				admin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
				admin.email.toLowerCase().includes(searchTerm.toLowerCase()),
		);
		setFilteredAdmins(filtered);
	}, [searchTerm, admins]);

	useEffect(() => {
		const handleClickOutside = (event) => {
			if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
				setIsOpen(false);
			}
		};

		document.addEventListener('mousedown', handleClickOutside);
		return () => document.removeEventListener('mousedown', handleClickOutside);
	}, []);

	useEffect(() => {
		if (preSelectedAdminId && admins.length > 0) {
			const adminExists = admins.some(
				(admin) => admin._id === preSelectedAdminId,
			);

			if (adminExists) {
				setInternalSelectedAdmin(preSelectedAdminId);
				onSelect(preSelectedAdminId);
			} else {
				console.warn(
					`preSelectedAdminId "${preSelectedAdminId}" not found in admins.`,
				);
			}
		}
	}, [preSelectedAdminId, admins, onSelect]);

	const handleSelect = (admin) => {
		onSelect(admin._id);
		setInternalSelectedAdmin(admin._id);
		setSearchTerm('');
		setIsOpen(false);
	};

	const selectedAdminInfo = admins.find(
		(admin) => admin._id === internalSelectedAdmin,
	);

	return (
		<div
			className='relative'
			ref={dropdownRef}>
			<div
				className='w-full px-4 py-2 rounded-lg bg-primary/5 text-text border border-secondary focus:outline-none focus:ring-2 focus:ring-accent cursor-pointer flex items-center justify-between'
				onClick={() => setIsOpen(!isOpen)}>
				<span className='truncate'>
					{selectedAdminInfo
						? `${selectedAdminInfo.name} (${selectedAdminInfo.email})`
						: 'Select a vendor...'}
				</span>
				{selectedAdminInfo && (
					<button
						onClick={(e) => {
							e.stopPropagation();
							onSelect('');
							setInternalSelectedAdmin('');
							setSearchTerm('');
						}}
						className='text-text/50 hover:text-accent ml-2'>
						<FaTimes className='h-4 w-4' />
					</button>
				)}
			</div>

			{isOpen && (
				<div className='absolute z-10 w-full mt-1 bg-background rounded-lg shadow-lg border border-secondary'>
					<div className='p-2'>
						<div className='relative'>
							<FaSearch className='absolute left-3 top-1/2 transform -translate-y-1/2 text-text/50' />
							<input
								type='text'
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className='w-full pl-10 pr-4 py-2 bg-background text-text rounded-lg focus:outline-none focus:ring-2 focus:ring-accent border border-secondary'
								placeholder='Search vendors...'
								onClick={(e) => e.stopPropagation()}
							/>
						</div>
					</div>

					<div className='max-h-60 overflow-y-auto'>
						{filteredAdmins.length > 0 ? (
							filteredAdmins.map((admin) => (
								<div
									key={admin._id}
									className='px-4 py-2 hover:bg-primary cursor-pointer border-b border-secondary last:border-b-0'
									onClick={() => handleSelect(admin)}>
									<div className='text-text'>{admin.name}</div>
									<div className='text-text/60 text-sm'>{admin.email}</div>
								</div>
							))
						) : (
							<div className='px-4 py-2 text-text/60'>No vendors found</div>
						)}
					</div>
				</div>
			)}
		</div>
	);
};

export default SearchableAdminSelect;
