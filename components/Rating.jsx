import React, { useState, useContext } from 'react';
import { Star, Loader2 } from 'lucide-react';
import { AuthContext } from '@/app/context/AuthContext';

const Rating = ({
	adminId,
	initialRating,
	onRatingUpdated,
	readOnly = false,
}) => {
	const [rating, setRating] = useState(initialRating || 0);
	const [hoverRating, setHoverRating] = useState(0);
	const { fetchWithToken } = useContext(AuthContext);
	const [status, setStatus] = useState('idle'); // 'idle', 'loading', 'success', 'error'
	const [errorMessage, setErrorMessage] = useState('');
	const [animateSuccess, setAnimateSuccess] = useState(false);

	const handleStarClick = async (selectedRating) => {
		if (readOnly || status === 'loading' || selectedRating === rating) return;

		setStatus('loading');
		setErrorMessage('');

		try {
			const response = await fetchWithToken(
				`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/users/${adminId}/rate`,
				{
					method: 'PUT',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({ rating: selectedRating }),
				},
			);

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.message || 'Failed to submit rating');
			}

			const data = await response.json();
			setRating(selectedRating);
			setStatus('success');
			setAnimateSuccess(true);
			setTimeout(() => setAnimateSuccess(false), 1500);

			if (onRatingUpdated) {
				onRatingUpdated(data.newRating);
			}
		} catch (error) {
			console.error('Error submitting rating:', error);
			setErrorMessage(error.message);
			setStatus('error');
		}
	};

	// Calculate precise position for half-stars
	const handleMouseMove = (event, star) => {
		if (readOnly) return;

		const { left, width } = event.currentTarget.getBoundingClientRect();
		const mouseX = event.clientX - left;
		const percent = mouseX / width;

		if (percent <= 0.5) {
			setHoverRating(star - 0.5);
		} else {
			setHoverRating(star);
		}
	};

	// Get star fill based on rating state
	const getStarState = (star) => {
		const activeRating = hoverRating || rating;

		if (star <= activeRating) {
			return 'full';
		} else if (star - 0.5 <= activeRating) {
			return 'half';
		}
		return 'empty';
	};

	return (
		<div className='relative'>
			<div
				className={`flex items-center gap-1 ${
					readOnly ? 'cursor-default' : 'cursor-pointer'
				} ${animateSuccess ? 'scale-110 transition-transform' : ''}`}>
				{[1, 2, 3, 4, 5].map((star) => {
					const starState = getStarState(star);

					return (
						<div
							key={star}
							className={`relative w-6 h-6 transition-all duration-200 ${
								readOnly ? '' : 'hover:scale-110'
							}`}
							onMouseMove={
								readOnly ? undefined : (e) => handleMouseMove(e, star)
							}
							onMouseLeave={readOnly ? undefined : () => setHoverRating(0)}
							onClick={
								readOnly
									? undefined
									: () => handleStarClick(hoverRating || rating)
							}
							aria-label={`Rate ${star} stars`}>
							{/* Background star (outline) */}
							<Star
								className='absolute top-0 left-0 w-6 h-6'
								fill='none'
								stroke='#FCD34D'
								strokeWidth={1.5}
							/>

							{/* Half star */}
							{starState === 'half' && (
								<div className='absolute top-0 left-0 w-3 h-6 overflow-hidden'>
									<Star
										className='absolute top-0 left-0 w-6 h-6'
										fill='#FCD34D'
										stroke='#FCD34D'
										strokeWidth={1.5}
									/>
								</div>
							)}

							{/* Full star */}
							{starState === 'full' && (
								<Star
									className='absolute top-0 left-0 w-6 h-6'
									fill='#FCD34D'
									stroke='#FCD34D'
									strokeWidth={1.5}
								/>
							)}
						</div>
					);
				})}

				{/* Loading spinner */}
				{status === 'loading' && (
					<div className='ml-2'>
						<Loader2 className='w-4 h-4 animate-spin text-gray-500' />
					</div>
				)}

				{/* Success indicator */}
				{status === 'success' && animateSuccess && (
					<span className='ml-2 text-sm text-green-500 transition-opacity'>
						✓
					</span>
				)}
			</div>

			{/* Error message */}
			{status === 'error' && (
				<p className='text-sm text-red-500 mt-1'>{errorMessage}</p>
			)}
		</div>
	);
};

export default Rating;
