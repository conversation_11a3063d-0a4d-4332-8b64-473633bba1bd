'use client';
import { useState, useEffect } from 'react';
import { Upload } from 'lucide-react';

const AnimatedUploadProgress = ({
	fileName = 'Final Project.pdf',
	initialProgress = 0,
	animationDuration = 5000,
}) => {
	const [progress, setProgress] = useState(initialProgress);

	useEffect(() => {
		// Start with initial progress
		setProgress(initialProgress);

		// Set up animation interval
		const interval = setInterval(() => {
			setProgress((prevProgress) => {
				// Increase progress by small increments
				const newProgress = prevProgress + 1;

				// Stop at 100%
				if (newProgress >= 100) {
					clearInterval(interval);
					return 100;
				}

				return newProgress;
			});
		}, animationDuration / (100 - initialProgress));

		// Clean up interval on unmount
		return () => clearInterval(interval);
	}, [initialProgress, animationDuration]);

	return (
		<div className='card bg-primary/10 p-4 rounded-lg'>
			<div className='flex items-center mb-2'>
				<div className='btn-3d bg-gradient-to-r from-primary to-accent p-2 rounded-full mr-4'>
					<Upload className='w-5 h-5 text-white' />
				</div>
				<div>
					<p className='font-medium text-text'>{fileName}</p>
				</div>
				<div className='ml-auto'>
					<span className='text-accent'>{progress}%</span>
				</div>
			</div>
			<div className='w-full bg-primary/20 rounded-full h-2'>
				<div
					className='bg-gradient-to-r from-primary to-accent h-2 rounded-full transition-all duration-300 ease-out'
					style={{ width: `${progress}%` }}></div>
			</div>
		</div>
	);
};

export default AnimatedUploadProgress;
