'use client';
import { AuthContext } from '@/app/context/AuthContext';
import { useData } from '@/app/context/DataContext';
import { useState, useContext, useEffect } from 'react';
import { FaUsers, FaUserMinus, FaUserPlus } from 'react-icons/fa';
import Pagination from '@/components/Pagination';

import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';

const UserList = () => {
	const { user, fetchWithToken } = useContext(AuthContext);
	const {
		users,
		loading: contextLoading,
		error: contextError,
		fetchData,
		updateUser: updateUserInContext,
		pagination,
		updatePagination,
	} = useData();
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState(null);
	const [userToToggle, setUserToToggle] = useState(null);
	const [userPagination, setUserPagination] = useState({
		currentPage: 1,
		limit: 10,
		totalPages: 1,
		totalCount: 0,
	});

	// Combine loading states
	const isLoading = loading || contextLoading.users;

	// Combine error states
	const displayError = error || contextError?.users;

	// Update local pagination state when context pagination changes
	useEffect(() => {
		if (pagination && pagination.users) {
			setUserPagination(pagination.users);
		}
	}, [pagination]);

	// Handle page change
	const handlePageChange = (page) => {
		updatePagination('users', { currentPage: page });
	};

	// Handle limit change
	const handleLimitChange = (limit) => {
		updatePagination('users', { limit, currentPage: 1 });
	};

	// Fetch users on component mount
	useEffect(() => {
		const loadUsers = async () => {
			setLoading(true);
			try {
				const result = await fetchData('users', true); // Force refresh
				if (!result.success) {
					throw new Error(result.error || 'Failed to fetch users');
				}
			} catch (err) {
				setError(err.message);
			} finally {
				setLoading(false);
			}
		};

		loadUsers();
	}, [fetchData]);

	const handleToggleAdmin = async (userId) => {
		// Check if the current user is a superAdmin
		if (!user?.superAdmin) {
			setError('Only Super Administrators can modify admin privileges');
			setUserToToggle(null);
			return;
		}

		try {
			const response = await fetchWithToken(
				`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/users/${userId}/set-admin`,
				{
					method: 'PATCH',
				},
			);

			if (!response.ok) {
				const errorData = await response.json().catch(() => ({}));
				throw new Error(errorData.message || 'Failed to update user role');
			}

			const { user: updatedUser } = await response.json();

			// Update user in context
			updateUserInContext(userId, { isAdmin: updatedUser.isAdmin });

			// Also refresh the user list to ensure consistency
			fetchData('users', true);

			setUserToToggle(null);
		} catch (error) {
			setError(error.message);
		}
	};

	const formatDate = (dateString) => {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'long',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit',
		});
	};

	// if (isLoading) {
	// 	return (
	// 		<div className='flex justify-center items-center min-h-[400px]'>
	// 			<div className='animate-spin rounded-full h-12 w-12 border-b-2 border-purple-700'></div>
	// 		</div>
	// 	);
	// }

	if (displayError) {
		return (
			<div className='text-center py-8 text-red-500 bg-red-50 rounded-lg'>
				<div className='text-xl font-semibold'>Error: {displayError}</div>
			</div>
		);
	}

	if (!users || !users.length) {
		return (
			<div className='flex flex-col items-center justify-center min-h-[400px] bg-white rounded-lg shadow-lg p-8'>
				<FaUsers className='text-purple-400 text-6xl mb-4' />
				<h3 className='text-2xl font-semibold text-gray-700 mb-2'>
					No Users Found
				</h3>
				<p className='text-gray-500 text-center'>
					There are currently no users in the system.
				</p>
			</div>
		);
	}

	return (
		<div className='p-6 bg-background rounded-lg shadow-lg card'>
			<h2 className='text-3xl font-bold text-purple-700 mb-6 text-center'>
				System Users
			</h2>

			{error && (
				<div className='mb-4 p-3 bg-red-50 text-red-500 rounded-md'>
					{error}
					<button
						className='ml-2 text-red-700 hover:underline'
						onClick={() => setError(null)}>
						Dismiss
					</button>
				</div>
			)}

			<div className='overflow-x-auto'>
				<table className='min-w-full bg-background border border-primary/20 shadow-md'>
					<thead className='bg-primary/10'>
						<tr>
							<th className='py-4 px-6 border-b text-left text-primary font-semibold'>
								Name
							</th>
							<th className='py-4 px-6 border-b text-left text-primary font-semibold'>
								Email
							</th>
							<th className='py-4 px-6 border-b text-left text-primary font-semibold'>
								Matric Number
							</th>
							<th className='py-4 px-6 border-b text-left text-primary font-semibold'>
								Joined On
							</th>
							<th className='py-4 px-6 border-b text-left text-primary font-semibold'>
								Status
							</th>
							<th className='py-4 px-6 border-b text-left text-primary font-semibold'>
								Actions
							</th>
						</tr>
					</thead>
					<tbody>
						{users.map((userData) => (
							<tr
								key={userData._id}
								className='hover:bg-primary transition-colors'>
								<td className='py-4 px-6 border-b font-medium'>
									{userData.name}
								</td>
								<td className='py-4 px-6 border-b'>{userData.email}</td>
								<td className='py-4 px-6 border-b'>
									{userData.matricNumber || '-'}
								</td>
								<td className='py-4 px-6 border-b'>
									{formatDate(userData.createdAt)}
								</td>
								<td className='py-4 px-6 border-b'>
									<span
										className={`px-3 py-1 rounded-full text-sm ${
											userData.isVerified
												? 'bg-green-100 text-green-800'
												: 'bg-yellow-100 text-yellow-800'
										}`}>
										{userData.isVerified ? 'Verified' : 'Pending'}
									</span>
									{userData.isAdmin && (
										<span className='ml-2 px-3 py-1 rounded-full text-sm bg-purple-100 text-purple-800'>
											Admin
										</span>
									)}
								</td>
								<td className='py-4 px-6 border-b'>
									{user?._id !== userData._id && user?.superAdmin && (
										<button
											onClick={() => setUserToToggle(userData)}
											className={`px-3 py-1.5 rounded-lg transition text-sm ${
												userData.isAdmin
													? 'bg-red-500 text-white hover:bg-red-600'
													: 'bg-purple-500 text-white hover:bg-purple-600'
											}`}>
											{userData.isAdmin ? (
												<span className='flex items-center gap-1'>
													<FaUserMinus /> Remove Admin
												</span>
											) : (
												<span className='flex items-center gap-1'>
													<FaUserPlus /> Make Admin
												</span>
											)}
										</button>
									)}
								</td>
							</tr>
						))}
					</tbody>
				</table>
			</div>

			{/* Pagination */}
			{!contextLoading.users && (
				<div className='mt-6'>
					<Pagination
						pagination={userPagination}
						onPageChange={handlePageChange}
						onLimitChange={handleLimitChange}
						itemName='users'
						currentItems={users || []}
					/>
				</div>
			)}

			<AlertDialog
				open={!!userToToggle}
				onOpenChange={() => setUserToToggle(null)}>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Confirm Role Change</AlertDialogTitle>
						<AlertDialogDescription>
							Are you sure you want to{' '}
							{userToToggle?.isAdmin ? 'remove' : 'grant'} admin privileges for{' '}
							{userToToggle?.name}? This will{' '}
							{userToToggle?.isAdmin ? 'restrict' : 'give them'} full access to
							the system.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							className={
								userToToggle?.isAdmin
									? 'bg-red-500 hover:bg-red-600'
									: 'bg-purple-500 hover:bg-purple-600'
							}
							onClick={() => handleToggleAdmin(userToToggle?._id)}>
							Confirm
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</div>
	);
};

export default UserList;
