'use client';
import { FaUpload, FaTimes } from 'react-icons/fa';

export const FileUpload = ({
	dragActive,
	onDrag,
	onDrop,
	onChange,
	selectedFileName,
	onClear,
	disabled,
}) => (
	<div
		onDragEnter={onDrag}
		onDragOver={onDrag}
		onDragLeave={onDrag}
		onDrop={onDrop}
		className={`border-2 border-dashed ${
			dragActive ? 'border-accent' : 'border-secondary'
		} rounded-lg p-6 text-center cursor-pointer transition-colors duration-300 bg-background`}>
		<input
			type='file'
			id='file-input'
			className='hidden'
			onChange={onChange}
			disabled={disabled}
		/>
		<label
			htmlFor='file-input'
			className='flex flex-col items-center justify-center space-y-2'>
			<FaUpload className='h-8 w-8 text-accent' />
			<p className='text-sm text-text/80'>
				{selectedFileName ? selectedFileName : 'Drag & drop or click to upload'}
			</p>
			{selectedFileName && (
				<button
					type='button'
					onClick={onClear}
					className='flex items-center gap-1 text-sm text-red-400 hover:text-red-300'>
					<FaTimes className='h-3 w-3' />
					Remove file
				</button>
			)}
		</label>
	</div>
);
