'use client';

import { useContext, useRef, useState, useEffect } from 'react';
import html2canvas from 'html2canvas';
import { FaPercentage } from 'react-icons/fa';
import { Download } from 'lucide-react';
import { AuthContext } from '@/app/context/AuthContext';
import Image from 'next/image';

export const PricingDisplay = ({
	pageCount,
	price,
	originalPrice,
	discount,
	title, // New prop for project title
}) => {
	const componentRef = useRef();
	const { user } = useContext(AuthContext);
	const [transactionId, setTransactionId] = useState('');

	// Generate transaction ID once when component mounts
	useEffect(() => {
		setTransactionId(Math.random().toString(36).substring(2, 10).toUpperCase());
	}, []);

	const downloadAsImage = async () => {
		if (!componentRef.current) return;

		// Create a temporary element to add the watermark
		const receiptContainer = document.createElement('div');
		receiptContainer.style.position = 'relative';
		receiptContainer.style.width = `${componentRef.current.offsetWidth}px`;
		receiptContainer.style.height = `${componentRef.current.offsetHeight}px`;
		receiptContainer.style.overflow = 'hidden';

		// Clone the receipt content
		const receiptClone = componentRef.current.cloneNode(true);
		receiptContainer.appendChild(receiptClone);

		// Add multiple watermarks in a grid pattern
		for (let i = 0; i < 5; i++) {
			for (let j = 0; j < 7; j++) {
				const watermark = document.createElement('div');
				watermark.innerText = 'UploadDoc';
				watermark.style.position = 'absolute';
				watermark.style.top = `${j * 20}%`;
				watermark.style.left = `${i * 25}%`;
				watermark.style.transform = 'rotate(-30deg)';
				watermark.style.fontSize = '16px';
				watermark.style.fontWeight = 'bold';
				watermark.style.opacity = '0.15';
				watermark.style.color = '#000';
				watermark.style.pointerEvents = 'none';
				watermark.style.zIndex = '1000';
				receiptContainer.appendChild(watermark);
			}
		}

		// Add to document temporarily
		document.body.appendChild(receiptContainer);

		// Generate image
		const canvas = await html2canvas(receiptContainer, {
			scale: 3,
			useCORS: true,
			backgroundColor: '#ffffff',
			logging: false,
			allowTaint: true,
		});

		// Remove temporary elements
		document.body.removeChild(receiptContainer);

		// Download image
		const image = canvas.toDataURL('image/png');
		const link = document.createElement('a');
		link.href = image;
		link.download = `${user?.name || 'Customer'}_receipt_${transactionId}.png`;
		link.click();
	};

	// Function to create receipt watermark pattern for web display
	const createWatermarkPattern = () => {
		const pattern = [];
		for (let i = 0; i < 5; i++) {
			for (let j = 0; j < 7; j++) {
				pattern.push(
					<div
						key={`watermark-${i}-${j}`}
						className='absolute pointer-events-none text-black opacity-10 font-bold text-sm'
						style={{
							top: `${j * 20}%`,
							left: `${i * 25}%`,
							transform: 'rotate(-30deg)',
						}}>
						<div className='flex items-center justify-center'>
							<Image
								src={'/icon.png'}
								width={12}
								height={12}
								className='rounded-full'
								alt='UploadDoc Logo'
								priority
							/>
						</div>
						UploadDoc
					</div>,
				);
			}
		}
		return pattern;
	};

	return (
		<div className='w-full'>
			{pageCount > 0 && (
				<div className='relative max-w-md mx-auto'>
					{/* Web watermark pattern */}
					<div className='absolute inset-0 overflow-hidden'>
						{createWatermarkPattern()}
					</div>

					<div
						ref={componentRef}
						className='bg-white rounded-lg shadow-lg p-6 border border-gray-200 max-w-md mx-auto relative z-10'>
						<div className='text-center mb-6'>
							<h2 className='text-2xl font-bold text-gray-800'>
								Pricing Receipt
							</h2>
							<p className='text-gray-500 text-sm'>
								{new Date().toLocaleDateString('en-US', {
									weekday: 'long',
									year: 'numeric',
									month: 'long',
									day: 'numeric',
								})}
							</p>
						</div>

						<div className='space-y-4'>
							{/* Project Title */}
							<div className='flex justify-between py-2 border-b border-gray-100'>
								<span className='font-medium text-gray-700'>
									Project Title:
								</span>
								<span className='text-gray-800 font-semibold'>
									{title || 'Untitled Document'}
								</span>
							</div>

							{/* User Name */}
							{user?.name && (
								<div className='flex justify-between py-2 border-b border-gray-100'>
									<span className='font-medium text-gray-700'>Customer:</span>
									<span className='text-gray-800'>{user.name}</span>
								</div>
							)}

							{/* Pages */}
							<div className='flex justify-between py-2 border-b border-gray-100'>
								<span className='font-medium text-gray-700'>Pages:</span>
								<span className='text-gray-800'>{pageCount}</span>
							</div>

							{/* Price */}
							<div className='flex justify-between py-2 border-b border-gray-100'>
								<span className='font-medium text-gray-700'>Total Price:</span>
								<div className='text-right'>
									{discount ? (
										<div>
											<span className='font-bold text-gray-800'>₦{price}</span>
											<span className='line-through text-gray-400 ml-2'>
												₦{originalPrice}
											</span>
										</div>
									) : (
										<span className='font-bold text-gray-800'>₦{price}</span>
									)}
								</div>
							</div>

							{/* Discount Details */}
							{discount && (
								<>
									<div className='flex justify-between py-2 border-b border-gray-100'>
										<div className='flex items-center'>
											<span className='font-medium text-gray-700 mr-1'>
												Discount:
											</span>
											<FaPercentage className='text-green-500' />
										</div>
										<div className='text-right'>
											<span className='text-green-500 font-medium'>
												{discount.percentage}% off
											</span>
											<p className='text-xs text-gray-500'>
												{discount.description}
											</p>
										</div>
									</div>

									<div className='flex justify-between py-2 border-b border-gray-100'>
										<span className='font-medium text-gray-700'>Savings:</span>
										<span className='text-green-500 font-medium'>
											₦{discount.savings}
										</span>
									</div>
								</>
							)}
						</div>

						<div className='mt-6 text-center text-sm text-gray-500'>
							<p className='font-medium'>Thank you for using UploadDoc!</p>
							<p>Transaction ID: {transactionId}</p>
						</div>
					</div>
				</div>
			)}

			<div className='mt-4 text-center'>
				<button
					onClick={downloadAsImage}
					disabled={pageCount === 0}
					className='flex items-center justify-center mx-auto bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-md transition-colors'
					aria-label='Download pricing receipt as image'>
					<Download className='w-4 h-4 mr-2' />
					Download Receipt
				</button>
			</div>
		</div>
	);
};
