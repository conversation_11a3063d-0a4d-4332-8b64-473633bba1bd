'use client';

import { Suspense } from 'react';
import { SubmitProjectForm } from './SubmitProjectForm';
import { FaSpinner } from 'react-icons/fa';

/**
 * Wrapper component that adds Suspense boundary around SubmitProjectForm
 * This is needed because SubmitProjectForm uses useSearchParams which requires Suspense
 */
export const SubmitProjectFormWrapper = (props) => {
  return (
    <Suspense
      fallback={
        <div className="bg-primary/5 backdrop-blur-md p-8 rounded-lg card w-full max-w-lg flex justify-center items-center min-h-[400px]">
          <div className="animate-spin text-primary">
            <FaSpinner className="h-8 w-8" />
          </div>
        </div>
      }
    >
      <SubmitProjectForm {...props} />
    </Suspense>
  );
};

export default SubmitProjectFormWrapper;
