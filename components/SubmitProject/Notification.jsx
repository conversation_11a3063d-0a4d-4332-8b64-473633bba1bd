'use client';
import { FaCheckCircle, FaExclamationCircle } from 'react-icons/fa';
import { Alert, AlertDescription } from '@/components/ui/alert';

export const Notification = ({ type, message }) => (
	<Alert
		className={`mb-4 ${
			type === 'success'
				? 'bg-green-500 bg-opacity-20 border-green-500'
				: 'bg-red-500 bg-opacity-20 border-red-500'
		}`}>
		{type === 'success' ? (
			<FaCheckCircle className='h-4 w-4' />
		) : (
			<FaExclamationCircle className='h-4 w-4' />
		)}
		<AlertDescription>{message}</AlertDescription>
	</Alert>
);
