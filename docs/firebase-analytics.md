# Firebase Analytics Implementation Guide

This document outlines how Firebase Analytics is implemented in the UploadDoc application.

## Setup

Firebase Analytics is initialized in the `AnalyticsContext.js` file, which provides a context for tracking events and page views throughout the application.

### Key Files

- `firebase.js` - Firebase app initialization
- `app/context/AnalyticsContext.js` - Analytics context provider
- `app/hooks/usePageTracking.js` - Hook for tracking page views
- `app/hooks/withAnalytics.js` - HOC for adding analytics to components

## Usage

### Tracking Page Views

There are two ways to track page views:

1. Using the `usePageTracking` hook:

```jsx
import usePageTracking from '@/app/hooks/usePageTracking';

const MyPage = () => {
  // Track page view with optional parameters
  usePageTracking('page_name', { param1: 'value1' });
  
  return <div>Page content</div>;
};
```

2. Using the `withAnalytics` HOC:

```jsx
import withAnalytics from '@/app/hooks/withAnalytics';

const MyPage = () => {
  return <div>Page content</div>;
};

// Wrap the component with analytics tracking
export default withAnalytics(MyPage, 'page_name', { param1: 'value1' });
```

### Tracking Events

To track custom events, use the `trackEvent` function from the `useAnalytics` hook:

```jsx
import { useAnalytics } from '@/app/context/AnalyticsContext';

const MyComponent = () => {
  const { trackEvent } = useAnalytics();
  
  const handleButtonClick = () => {
    // Track the event
    trackEvent('button_click', { button_name: 'submit' });
    
    // Perform the action
    submitForm();
  };
  
  return <button onClick={handleButtonClick}>Submit</button>;
};
```

## Standard Events

The application tracks the following standard events:

### Authentication Events

- `login_attempt` - When a user attempts to log in
- `login_success` - When a user successfully logs in
- `login_failure` - When a login attempt fails
- `registration_attempt` - When a user attempts to register
- `registration_success` - When a user successfully registers
- `registration_failure` - When a registration attempt fails

### Navigation Events

- `page_view` - When a user views a page
- `component_mounted` - When a component mounts
- `component_unmounted` - When a component unmounts

### Document Events

- `document_submission_started` - When a user starts submitting a document
- `document_submission_success` - When a document is successfully submitted
- `document_submission_failure` - When a document submission fails

### User Interaction Events

- `button_click` - When a user clicks a button
- `form_submit` - When a user submits a form
- `filter_change` - When a user changes a filter
- `search_query` - When a user performs a search

## User Properties

The following user properties are tracked:

- `isAdmin` - Whether the user is an admin
- `isSuperAdmin` - Whether the user is a super admin
- `email` - The user's email address
- `name` - The user's name

## Best Practices

1. **Consistent Naming**: Use snake_case for event names and parameters
2. **Descriptive Names**: Use descriptive names for events and parameters
3. **Limit Parameters**: Limit the number of parameters per event
4. **Avoid PII**: Do not track personally identifiable information
5. **Error Handling**: Always wrap analytics calls in try/catch blocks

## Debugging

To debug Firebase Analytics:

1. Enable debug mode in the Firebase console
2. Use the Firebase Analytics debugger in the browser
3. Check the browser console for analytics-related logs

## Resources

- [Firebase Analytics Documentation](https://firebase.google.com/docs/analytics)
- [Firebase Analytics Events](https://firebase.google.com/docs/analytics/events)
- [Firebase Analytics User Properties](https://firebase.google.com/docs/analytics/user-properties)
